<template>
  <div class="edit-request">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="request" class="edit-container">
      <el-card class="edit-card">
        <template #header>
          <div class="card-header">
            <h2>编辑需求</h2>
            <p>修改你的需求信息</p>
          </div>
        </template>

        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="edit-form"
        >
          <!-- 需求类型选择 -->
          <el-form-item label="需求类型" prop="typeId">
            <div class="type-selector">
              <el-radio-group v-model="form.typeId" class="type-group">
                <el-radio-button
                  v-for="type in requestTypes"
                  :key="type.id"
                  :value="type.id"
                  class="type-button"
                >
                  <el-icon class="type-icon">
                    <component :is="type.icon" />
                  </el-icon>
                  <span>{{ type.name }}</span>
                </el-radio-button>
              </el-radio-group>
            </div>
          </el-form-item>

          <!-- 需求标题 -->
          <el-form-item label="需求标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="简洁明了地描述你的需求"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <!-- 需求描述 -->
          <el-form-item label="详细描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="详细描述你的需求，包括时间、地点、具体要求等"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <!-- 时间设置 -->
          <el-form-item label="时间安排" prop="timeRange">
            <div class="time-setting">
              <el-date-picker
                v-model="form.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
              />
            </div>
          </el-form-item>

          <!-- 位置信息 -->
          <el-form-item label="位置信息" prop="location">
            <location-selector v-model="form.location" />
          </el-form-item>

          <!-- 参与人数 -->
          <el-form-item label="参与人数" prop="maxParticipants">
            <el-input-number
              v-model="form.maxParticipants"
              :min="form.currentParticipants || 1"
              :max="10"
              controls-position="right"
            />
            <span class="form-tip">
              当前已有 {{ form.currentParticipants || 0 }} 人参与
            </span>
          </el-form-item>

          <!-- 偏好设置 -->
          <el-form-item label="偏好设置">
            <div class="preference-setting">
              <div class="preference-item">
                <label>性别偏好：</label>
                <el-radio-group v-model="form.genderPreference">
                  <el-radio :value="0">无偏好</el-radio>
                  <el-radio :value="1">男生</el-radio>
                  <el-radio :value="2">女生</el-radio>
                </el-radio-group>
              </div>
              
              <div class="preference-item">
                <label>年级偏好：</label>
                <el-select
                  v-model="form.gradePreference"
                  multiple
                  placeholder="选择年级偏好"
                  style="width: 200px"
                >
                  <el-option label="大一" value="2024" />
                  <el-option label="大二" value="2023" />
                  <el-option label="大三" value="2022" />
                  <el-option label="大四" value="2021" />
                  <el-option label="研一" value="2024-master" />
                  <el-option label="研二" value="2023-master" />
                  <el-option label="研三" value="2022-master" />
                </el-select>
              </div>
            </div>
          </el-form-item>

          <!-- 附加条件卡片 -->
          <el-form-item label="附加条件">
            <condition-cards
              v-model="form.conditions"
              :templates="conditionTemplates"
            />
          </el-form-item>

          <!-- 标签 -->
          <el-form-item label="标签">
            <tag-input v-model="form.tags" />
          </el-form-item>

          <!-- 需求状态 -->
          <el-form-item label="需求状态">
            <el-radio-group v-model="form.status">
              <el-radio :value="0">待匹配</el-radio>
              <el-radio :value="1">匹配中</el-radio>
              <el-radio :value="3">已取消</el-radio>
            </el-radio-group>
            <div class="status-tip">
              <el-text size="small" type="info">
                修改状态会影响其他用户的查看和申请
              </el-text>
            </div>
          </el-form-item>

          <!-- 操作按钮 -->
          <el-form-item>
            <div class="form-actions">
              <el-button @click="handleCancel">取消</el-button>
              <el-button @click="handleSaveDraft">保存草稿</el-button>
              <el-button
                type="primary"
                @click="handleUpdate"
                :loading="updating"
              >
                更新需求
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="需求不存在"
        sub-title="该需求可能已被删除或你没有编辑权限"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useBuddyRequestStore } from '@/stores/buddy-request'
import { useUserStore } from '@/stores/user'
import LocationSelector from '@/components/request/LocationSelector.vue'
import ConditionCards from '@/components/request/ConditionCards.vue'
import TagInput from '@/components/request/TagInput.vue'

const route = useRoute()
const router = useRouter()
const buddyRequestStore = useBuddyRequestStore()
const userStore = useUserStore()

// 响应式数据
const formRef = ref()
const loading = ref(true)
const updating = ref(false)

// 表单数据
const form = reactive({
  typeId: null,
  title: '',
  description: '',
  timeRange: [],
  location: null,
  maxParticipants: 1,
  currentParticipants: 0,
  genderPreference: 0,
  gradePreference: [],
  conditions: [],
  tags: [],
  status: 0
})

// 表单验证规则
const rules = {
  typeId: [
    { required: true, message: '请选择需求类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入需求标题', trigger: 'blur' },
    { min: 5, max: 50, message: '标题长度在 5 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入需求描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
  ],
  timeRange: [
    { required: true, message: '请选择时间范围', trigger: 'change' }
  ],
  location: [
    { required: true, message: '请选择位置信息', trigger: 'change' }
  ],
  maxParticipants: [
    { required: true, message: '请设置参与人数', trigger: 'blur' }
  ]
}

// 计算属性
const request = computed(() => buddyRequestStore.currentRequest)
const requestTypes = computed(() => buddyRequestStore.requestTypes)
const conditionTemplates = computed(() => buddyRequestStore.conditionTemplates)

const isOwner = computed(() => {
  return userStore.user?.id === request.value?.userId
})

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7
}

// 方法
const goBack = () => {
  router.back()
}

const handleCancel = () => {
  ElMessageBox.confirm('确定要取消编辑吗？未保存的修改将丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    goBack()
  }).catch(() => {
    // 用户取消
  })
}

const handleSaveDraft = () => {
  // 保存到本地存储
  const draftKey = `buddy_request_edit_draft_${route.params.id}`
  localStorage.setItem(draftKey, JSON.stringify(form))
  ElMessage.success('草稿已保存')
}

const handleUpdate = async () => {
  try {
    await formRef.value.validate()
    
    updating.value = true
    
    // 构造更新数据
    const updateData = {
      typeId: form.typeId,
      title: form.title,
      description: form.description,
      startTime: form.timeRange[0],
      endTime: form.timeRange[1],
      location: form.location,
      maxParticipants: form.maxParticipants,
      genderPreference: form.genderPreference,
      gradePreference: form.gradePreference,
      tags: form.tags,
      conditions: form.conditions,
      status: form.status
    }
    
    const result = await buddyRequestStore.updateRequest(route.params.id, updateData)
    
    if (result.success) {
      // 清除草稿
      const draftKey = `buddy_request_edit_draft_${route.params.id}`
      localStorage.removeItem(draftKey)
      
      // 跳转到需求详情页
      router.push(`/request/${route.params.id}`)
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    updating.value = false
  }
}

const loadFormData = () => {
  if (!request.value) return
  
  // 填充表单数据
  form.typeId = request.value.typeId
  form.title = request.value.title
  form.description = request.value.description
  form.timeRange = [request.value.startTime, request.value.endTime]
  form.location = request.value.location
  form.maxParticipants = request.value.maxParticipants
  form.currentParticipants = request.value.currentParticipants
  form.genderPreference = request.value.genderPreference || 0
  form.gradePreference = request.value.gradePreference || []
  form.conditions = request.value.conditions || []
  form.tags = request.value.tags || []
  form.status = request.value.status
}

const loadDraft = () => {
  const draftKey = `buddy_request_edit_draft_${route.params.id}`
  const draft = localStorage.getItem(draftKey)
  if (draft) {
    try {
      const draftData = JSON.parse(draft)
      Object.assign(form, draftData)
      ElMessage.info('已加载上次保存的草稿')
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  }
}

// 初始化
onMounted(async () => {
  try {
    // 获取需求详情
    await buddyRequestStore.fetchRequestDetail(route.params.id)
    
    // 检查权限
    if (!isOwner.value) {
      ElMessage.error('你没有编辑此需求的权限')
      goBack()
      return
    }
    
    // 获取需求类型和条件模板
    await Promise.all([
      buddyRequestStore.fetchRequestTypes(),
      buddyRequestStore.fetchConditionTemplates()
    ])
    
    // 加载表单数据
    loadFormData()
    
    // 尝试加载草稿
    loadDraft()
  } catch (error) {
    console.error('初始化失败:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
.edit-request {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  
  .loading-container {
    padding: 40px 0;
  }
  
  .edit-card {
    .card-header {
      text-align: center;
      
      h2 {
        margin: 0 0 8px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
  }
  
  .edit-form {
    margin-top: 20px;
  }
  
  .type-selector {
    .type-group {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
    
    .type-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 16px;
      border-radius: 8px;
      
      .type-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }
    }
  }
  
  .time-setting {
    width: 100%;
  }
  
  .preference-setting {
    .preference-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      label {
        width: 80px;
        font-weight: 500;
      }
    }
  }
  
  .form-tip {
    margin-left: 8px;
    color: var(--el-text-color-regular);
    font-size: 12px;
  }
  
  .status-tip {
    margin-top: 8px;
  }
  
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
  }
  
  .error-container {
    padding: 60px 0;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .edit-request {
    padding: 12px;
  }
  
  .type-group {
    justify-content: center;
  }
  
  .type-button {
    min-width: 80px;
  }
}
</style>
