<template>
  <div class="request-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 需求详情 -->
    <div v-else-if="request" class="detail-container">
      <!-- 返回按钮 -->
      <div class="back-navigation">
        <el-button @click="goBack" class="back-btn">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>

      <!-- 需求卡片 -->
      <div class="request-card-container">
        <request-card
          :request="request"
          :is-detail-view="true"
          @update="handleRequestUpdate"
          @delete="handleRequestDelete"
        />
      </div>

      <!-- 详细信息 -->
      <div class="detail-sections">
        <!-- 需求详情 -->
        <el-card class="detail-section">
          <template #header>
            <h3>需求详情</h3>
          </template>
          
          <div class="detail-content">
            <div class="detail-item">
              <label>需求类型：</label>
              <el-tag :type="getRequestTypeColor(request.type)">
                <el-icon class="tag-icon">
                  <component :is="request.type?.icon" />
                </el-icon>
                {{ request.type?.name }}
              </el-tag>
            </div>
            
            <div class="detail-item">
              <label>活动时间：</label>
              <span>{{ formatTimeRange(request.startTime, request.endTime) }}</span>
            </div>
            
            <div class="detail-item">
              <label>活动地点：</label>
              <span>{{ request.location?.name }} - {{ request.location?.description }}</span>
            </div>
            
            <div class="detail-item">
              <label>参与人数：</label>
              <span>{{ request.currentParticipants }}/{{ request.maxParticipants }}人</span>
              <el-progress
                :percentage="(request.currentParticipants / request.maxParticipants) * 100"
                :stroke-width="6"
                class="progress-bar"
              />
            </div>
            
            <div v-if="request.genderPreference !== 0" class="detail-item">
              <label>性别偏好：</label>
              <span>{{ getGenderPreferenceText(request.genderPreference) }}</span>
            </div>
            
            <div v-if="request.gradePreference && request.gradePreference.length > 0" class="detail-item">
              <label>年级偏好：</label>
              <div class="grade-tags">
                <el-tag
                  v-for="grade in request.gradePreference"
                  :key="grade"
                  size="small"
                  class="grade-tag"
                >
                  {{ getGradeText(grade) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 附加条件 -->
        <el-card v-if="request.conditions && request.conditions.length > 0" class="detail-section">
          <template #header>
            <h3>附加条件</h3>
          </template>
          
          <div class="conditions-grid">
            <div
              v-for="condition in request.conditions"
              :key="condition.id"
              class="condition-card"
            >
              <el-icon class="condition-icon">
                <component :is="condition.icon" />
              </el-icon>
              <div class="condition-content">
                <span class="condition-text">{{ condition.text }}</span>
                <span v-if="condition.value" class="condition-value">{{ condition.value }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 发布者信息 -->
        <el-card class="detail-section">
          <template #header>
            <h3>发布者信息</h3>
          </template>
          
          <div class="publisher-info">
            <div class="publisher-avatar">
              <el-avatar :src="request.user?.avatar" :size="60">
                {{ getAvatarText(request.user?.nickname) }}
              </el-avatar>
            </div>
            
            <div class="publisher-details">
              <div class="publisher-name">{{ getAnonymousName(request.user) }}</div>
              <div class="publisher-meta">
                <span class="meta-item">{{ request.user?.grade }}</span>
                <span class="meta-item">{{ request.user?.college }}</span>
              </div>
              <div class="publisher-stats">
                <span class="stat-item">发布需求 {{ request.user?.requestCount || 0 }} 次</span>
                <span class="stat-item">信用评分 {{ request.user?.creditScore || 100 }}</span>
              </div>
            </div>
            
            <div class="publisher-actions">
              <el-button
                v-if="!isOwner"
                type="primary"
                @click="handleContact"
                class="contact-btn"
              >
                <el-icon><ChatDotRound /></el-icon>
                联系TA
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 申请列表（仅发布者可见） -->
        <el-card v-if="isOwner && applications.length > 0" class="detail-section">
          <template #header>
            <h3>申请列表 ({{ applications.length }})</h3>
          </template>
          
          <div class="applications-list">
            <div
              v-for="application in applications"
              :key="application.id"
              class="application-item"
            >
              <div class="applicant-info">
                <el-avatar :src="application.user?.avatar" :size="40">
                  {{ getAvatarText(application.user?.nickname) }}
                </el-avatar>
                <div class="applicant-details">
                  <div class="applicant-name">{{ getAnonymousName(application.user) }}</div>
                  <div class="applicant-meta">{{ application.user?.grade }} · {{ application.user?.college }}</div>
                </div>
              </div>
              
              <div class="application-content">
                <div class="application-text">
                  <p><strong>自我介绍：</strong>{{ application.introduction }}</p>
                  <p><strong>申请理由：</strong>{{ application.reason }}</p>
                  <p v-if="application.experience"><strong>相关经验：</strong>{{ application.experience }}</p>
                </div>
                <div class="application-time">
                  申请时间：{{ formatTime(application.createdAt) }}
                </div>
              </div>
              
              <div class="application-actions">
                <el-button
                  type="success"
                  size="small"
                  @click="handleAcceptApplication(application)"
                >
                  接受
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleRejectApplication(application)"
                >
                  拒绝
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 相关推荐 -->
        <el-card class="detail-section">
          <template #header>
            <h3>相关推荐</h3>
          </template>
          
          <div class="related-requests">
            <div
              v-for="relatedRequest in relatedRequests"
              :key="relatedRequest.id"
              class="related-item"
              @click="goToRequest(relatedRequest.id)"
            >
              <div class="related-header">
                <el-tag :type="getRequestTypeColor(relatedRequest.type)" size="small">
                  {{ relatedRequest.type?.name }}
                </el-tag>
                <span class="related-time">{{ formatTime(relatedRequest.createdAt) }}</span>
              </div>
              <div class="related-title">{{ relatedRequest.title }}</div>
              <div class="related-info">
                <span class="info-item">
                  <el-icon><Location /></el-icon>
                  {{ relatedRequest.location?.name }}
                </span>
                <span class="info-item">
                  <el-icon><User /></el-icon>
                  {{ relatedRequest.currentParticipants }}/{{ relatedRequest.maxParticipants }}人
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="需求不存在"
        sub-title="该需求可能已被删除或不存在"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useBuddyRequestStore } from '@/stores/buddy-request'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import RequestCard from '@/components/request/RequestCard.vue'
import {
  ArrowLeft,
  ChatDotRound,
  Location,
  User
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const buddyRequestStore = useBuddyRequestStore()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const applications = ref([])
const relatedRequests = ref([])

// 计算属性
const request = computed(() => buddyRequestStore.currentRequest)

const isOwner = computed(() => {
  return userStore.user?.id === request.value?.userId
})

// 方法
const goBack = () => {
  router.back()
}

const goToRequest = (id) => {
  router.push(`/request/${id}`)
}

const getRequestTypeColor = (type) => {
  const colorMap = {
    '学习搭子': 'primary',
    '运动搭子': 'success',
    '饭友': 'warning',
    '图书馆搭子': 'info'
  }
  return colorMap[type?.name] || 'primary'
}

const getGenderPreferenceText = (preference) => {
  const map = { 1: '男生', 2: '女生' }
  return map[preference] || '无偏好'
}

const getGradeText = (grade) => {
  const map = {
    '2024': '大一',
    '2023': '大二',
    '2022': '大三',
    '2021': '大四',
    '2024-master': '研一',
    '2023-master': '研二',
    '2022-master': '研三'
  }
  return map[grade] || grade
}

const getAvatarText = (nickname) => {
  return nickname ? nickname.charAt(0).toUpperCase() : '?'
}

const getAnonymousName = (user) => {
  if (!user?.nickname) return '匿名用户'
  const name = user.nickname
  if (name.length <= 2) return name.charAt(0) + '*'
  return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
}

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const formatTimeRange = (startTime, endTime) => {
  if (!startTime) return ''
  
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : null
  
  const formatDateTime = (date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
  }
  
  if (end) {
    return `${formatDateTime(start)} 至 ${formatDateTime(end)}`
  }
  
  return formatDateTime(start)
}

const handleRequestUpdate = () => {
  // 重新获取需求详情
  fetchRequestDetail()
}

const handleRequestDelete = () => {
  router.push('/square')
}

const handleContact = () => {
  // 跳转到私信页面
  router.push(`/messages/chat/${request.value.userId}`)
}

const handleAcceptApplication = async (application) => {
  try {
    await ElMessageBox.confirm('确定要接受这个申请吗？', '确认接受', {
      confirmButtonText: '接受',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    // 调用接受申请的API
    ElMessage.success('申请已接受')
    // 刷新申请列表
    fetchApplications()
  } catch (error) {
    // 用户取消
  }
}

const handleRejectApplication = async (application) => {
  try {
    await ElMessageBox.confirm('确定要拒绝这个申请吗？', '确认拒绝', {
      confirmButtonText: '拒绝',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 调用拒绝申请的API
    ElMessage.success('申请已拒绝')
    // 刷新申请列表
    fetchApplications()
  } catch (error) {
    // 用户取消
  }
}

const fetchRequestDetail = async () => {
  loading.value = true
  try {
    const requestId = route.params.id
    await buddyRequestStore.fetchRequestDetail(requestId)
    
    // 如果是发布者，获取申请列表
    if (isOwner.value) {
      await fetchApplications()
    }
    
    // 获取相关推荐
    await fetchRelatedRequests()
  } catch (error) {
    console.error('获取需求详情失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchApplications = async () => {
  // 模拟获取申请列表
  applications.value = []
}

const fetchRelatedRequests = async () => {
  // 模拟获取相关推荐
  relatedRequests.value = []
}

// 初始化
onMounted(() => {
  fetchRequestDetail()
})
</script>

<style lang="scss" scoped>
.request-detail {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  
  .loading-container {
    padding: 40px 0;
  }
  
  .back-navigation {
    margin-bottom: 20px;
    
    .back-btn {
      .el-icon {
        margin-right: 6px;
      }
    }
  }
  
  .request-card-container {
    margin-bottom: 24px;
  }
  
  .detail-sections {
    .detail-section {
      margin-bottom: 24px;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .detail-content {
      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        
        label {
          width: 100px;
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
        
        .tag-icon {
          margin-right: 4px;
        }
        
        .progress-bar {
          margin-left: 12px;
          flex: 1;
          max-width: 200px;
        }
        
        .grade-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
        }
      }
    }
    
    .conditions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 16px;
      
      .condition-card {
        display: flex;
        align-items: center;
        padding: 12px;
        background: var(--el-fill-color-lighter);
        border-radius: 8px;
        
        .condition-icon {
          font-size: 20px;
          color: var(--el-color-primary);
          margin-right: 12px;
        }
        
        .condition-content {
          flex: 1;
          
          .condition-text {
            display: block;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
          
          .condition-value {
            display: block;
            font-size: 12px;
            color: var(--el-color-primary);
            margin-top: 2px;
          }
        }
      }
    }
    
    .publisher-info {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .publisher-details {
        flex: 1;
        
        .publisher-name {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }
        
        .publisher-meta {
          color: var(--el-text-color-regular);
          margin-bottom: 8px;
          
          .meta-item {
            margin-right: 12px;
          }
        }
        
        .publisher-stats {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          
          .stat-item {
            margin-right: 16px;
          }
        }
      }
      
      .contact-btn {
        .el-icon {
          margin-right: 6px;
        }
      }
    }
    
    .applications-list {
      .application-item {
        display: flex;
        gap: 16px;
        padding: 16px;
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        margin-bottom: 16px;
        
        .applicant-info {
          display: flex;
          align-items: center;
          gap: 12px;
          min-width: 150px;
          
          .applicant-details {
            .applicant-name {
              font-weight: 500;
              color: var(--el-text-color-primary);
            }
            
            .applicant-meta {
              font-size: 12px;
              color: var(--el-text-color-regular);
            }
          }
        }
        
        .application-content {
          flex: 1;
          
          .application-text {
            margin-bottom: 8px;
            
            p {
              margin: 0 0 8px 0;
              font-size: 14px;
              line-height: 1.5;
              
              strong {
                color: var(--el-text-color-primary);
              }
            }
          }
          
          .application-time {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
          }
        }
        
        .application-actions {
          display: flex;
          flex-direction: column;
          gap: 8px;
          min-width: 80px;
        }
      }
    }
    
    .related-requests {
      .related-item {
        padding: 16px;
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: var(--el-color-primary);
          background: var(--el-color-primary-light-9);
        }
        
        .related-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .related-time {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
          }
        }
        
        .related-title {
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }
        
        .related-info {
          display: flex;
          gap: 16px;
          
          .info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--el-text-color-regular);
            
            .el-icon {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }
  }
  
  .error-container {
    padding: 60px 0;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .request-detail {
    padding: 12px;
    
    .detail-content {
      .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        
        label {
          width: auto;
        }
        
        .progress-bar {
          margin-left: 0;
          width: 100%;
          max-width: none;
        }
      }
    }
    
    .conditions-grid {
      grid-template-columns: 1fr;
    }
    
    .publisher-info {
      flex-direction: column;
      text-align: center;
    }
    
    .application-item {
      flex-direction: column;
      
      .application-actions {
        flex-direction: row;
        justify-content: center;
      }
    }
  }
}
</style>
