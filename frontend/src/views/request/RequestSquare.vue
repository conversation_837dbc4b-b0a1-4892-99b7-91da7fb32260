<template>
  <div class="request-square">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>需求广场</h1>
        <p>发现志同道合的校园搭子</p>
      </div>
      
      <div class="header-actions">
        <el-button
          type="primary"
          @click="goToPublish"
          class="publish-btn"
        >
          <el-icon><Plus /></el-icon>
          发布需求
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <div class="filter-content">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索需求标题、描述或标签"
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button @click="handleSearch">搜索</el-button>
              </template>
            </el-input>
          </div>

          <!-- 筛选条件 -->
          <div class="filter-options">
            <!-- 需求类型 -->
            <div class="filter-group">
              <label>类型：</label>
              <el-select
                v-model="filters.typeId"
                placeholder="全部类型"
                clearable
                @change="handleFilterChange"
                class="filter-select"
              >
                <el-option
                  v-for="type in requestTypes"
                  :key="type.id"
                  :label="type.name"
                  :value="type.id"
                >
                  <el-icon class="option-icon">
                    <component :is="type.icon" />
                  </el-icon>
                  {{ type.name }}
                </el-option>
              </el-select>
            </div>

            <!-- 排序方式 -->
            <div class="filter-group">
              <label>排序：</label>
              <el-select
                v-model="filters.sortBy"
                @change="handleFilterChange"
                class="filter-select"
              >
                <el-option label="最新发布" value="created_at" />
                <el-option label="最多点赞" value="like_count" />
                <el-option label="最多收藏" value="favorite_count" />
                <el-option label="即将开始" value="start_time" />
              </el-select>
            </div>

            <!-- 状态筛选 -->
            <div class="filter-group">
              <label>状态：</label>
              <el-select
                v-model="filters.status"
                @change="handleFilterChange"
                class="filter-select"
              >
                <el-option label="待匹配" :value="0" />
                <el-option label="匹配中" :value="1" />
                <el-option label="全部" :value="null" />
              </el-select>
            </div>

            <!-- 时间筛选 -->
            <div class="filter-group">
              <label>时间：</label>
              <el-select
                v-model="filters.timeFilter"
                @change="handleFilterChange"
                class="filter-select"
              >
                <el-option label="全部时间" value="" />
                <el-option label="今天" value="today" />
                <el-option label="明天" value="tomorrow" />
                <el-option label="本周" value="week" />
                <el-option label="本月" value="month" />
              </el-select>
            </div>
          </div>

          <!-- 快速筛选标签 -->
          <div class="quick-filters">
            <el-tag
              v-for="tag in quickFilterTags"
              :key="tag.value"
              :type="filters.quickFilter === tag.value ? 'primary' : 'info'"
              @click="handleQuickFilter(tag.value)"
              class="quick-filter-tag"
            >
              {{ tag.label }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 需求列表 -->
    <div class="request-list">
      <!-- 列表头部 -->
      <div class="list-header">
        <div class="list-info">
          <span class="total-count">共 {{ total }} 个需求</span>
          <el-divider direction="vertical" />
          <span class="filter-info">{{ getFilterInfo() }}</span>
        </div>
        
        <div class="view-options">
          <el-radio-group v-model="viewMode" @change="handleViewModeChange">
            <el-radio-button value="card">卡片视图</el-radio-button>
            <el-radio-button value="list">列表视图</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && requests.length === 0" class="loading-state">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading && requests.length === 0" class="empty-state">
        <el-empty description="暂无需求">
          <el-button type="primary" @click="goToPublish">发布第一个需求</el-button>
        </el-empty>
      </div>

      <!-- 需求卡片列表 -->
      <div v-else class="request-grid" :class="{ 'list-view': viewMode === 'list' }">
        <request-card
          v-for="request in requests"
          :key="request.id"
          :request="request"
          @update="handleRequestUpdate"
          @delete="handleRequestDelete"
          class="request-item"
        />
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <el-button
          v-if="!loadingMore"
          @click="loadMore"
          class="load-more-btn"
        >
          加载更多
        </el-button>
        <div v-else class="loading-more">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 回到顶部 -->
    <el-backtop :right="40" :bottom="40" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBuddyRequestStore } from '@/stores/buddy-request'
import RequestCard from '@/components/request/RequestCard.vue'
import {
  Plus,
  Search,
  Loading
} from '@element-plus/icons-vue'

const router = useRouter()
const buddyRequestStore = useBuddyRequestStore()

// 响应式数据
const searchKeyword = ref('')
const viewMode = ref('card')
const loadingMore = ref(false)

const filters = reactive({
  typeId: null,
  sortBy: 'created_at',
  status: 0,
  timeFilter: '',
  quickFilter: '',
  page: 1,
  size: 20
})

// 快速筛选标签
const quickFilterTags = [
  { label: '热门需求', value: 'hot' },
  { label: '推荐需求', value: 'recommended' },
  { label: '即将开始', value: 'upcoming' },
  { label: '长期需求', value: 'long_term' }
]

// 计算属性
const requests = computed(() => buddyRequestStore.requests)
const requestTypes = computed(() => buddyRequestStore.requestTypes)
const loading = computed(() => buddyRequestStore.loading)
const total = computed(() => buddyRequestStore.total)

const hasMore = computed(() => {
  return requests.value.length < total.value
})

// 方法
const goToPublish = () => {
  router.push('/publish')
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    buddyRequestStore.searchRequests(searchKeyword.value.trim(), {
      ...filters,
      page: 1
    })
  } else {
    handleFilterChange()
  }
}

const handleFilterChange = () => {
  filters.page = 1
  buddyRequestStore.fetchRequests(filters)
}

const handleQuickFilter = (filterValue) => {
  if (filters.quickFilter === filterValue) {
    filters.quickFilter = ''
  } else {
    filters.quickFilter = filterValue
  }
  
  // 根据快速筛选调用不同的API
  switch (filterValue) {
    case 'hot':
      buddyRequestStore.fetchHotRequests({ ...filters, page: 1 })
      break
    case 'recommended':
      buddyRequestStore.fetchRecommendedRequests({ ...filters, page: 1 })
      break
    default:
      handleFilterChange()
  }
}

const handleViewModeChange = () => {
  // 保存用户偏好到本地存储
  localStorage.setItem('request_view_mode', viewMode.value)
}

const handleRequestUpdate = () => {
  // 刷新当前页数据
  buddyRequestStore.fetchRequests({ ...filters, page: 1 })
}

const handleRequestDelete = (requestId) => {
  // 从列表中移除已删除的需求
  const index = requests.value.findIndex(req => req.id === requestId)
  if (index !== -1) {
    requests.value.splice(index, 1)
  }
}

const loadMore = async () => {
  if (loadingMore.value || !hasMore.value) return
  
  loadingMore.value = true
  try {
    filters.page += 1
    await buddyRequestStore.fetchRequests(filters)
  } catch (error) {
    console.error('加载更多失败:', error)
    filters.page -= 1
  } finally {
    loadingMore.value = false
  }
}

const getFilterInfo = () => {
  const infos = []
  
  if (filters.typeId) {
    const type = requestTypes.value.find(t => t.id === filters.typeId)
    if (type) infos.push(type.name)
  }
  
  if (filters.status !== null) {
    const statusMap = { 0: '待匹配', 1: '匹配中' }
    infos.push(statusMap[filters.status])
  }
  
  if (filters.timeFilter) {
    const timeMap = {
      today: '今天',
      tomorrow: '明天',
      week: '本周',
      month: '本月'
    }
    infos.push(timeMap[filters.timeFilter])
  }
  
  if (filters.quickFilter) {
    const quickTag = quickFilterTags.find(tag => tag.value === filters.quickFilter)
    if (quickTag) infos.push(quickTag.label)
  }
  
  return infos.length > 0 ? infos.join(' · ') : '全部需求'
}

// 滚动加载
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight
  
  if (scrollTop + windowHeight >= documentHeight - 100) {
    loadMore()
  }
}

// 初始化
onMounted(async () => {
  // 恢复用户偏好
  const savedViewMode = localStorage.getItem('request_view_mode')
  if (savedViewMode) {
    viewMode.value = savedViewMode
  }
  
  // 获取需求类型
  await buddyRequestStore.fetchRequestTypes()
  
  // 获取需求列表
  await buddyRequestStore.fetchRequests(filters)
  
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
.request-square {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 16px;
      }
    }
    
    .publish-btn {
      font-size: 16px;
      padding: 12px 24px;
      
      .el-icon {
        margin-right: 6px;
      }
    }
  }
  
  .filter-section {
    margin-bottom: 24px;
    
    .filter-card {
      .filter-content {
        .search-box {
          margin-bottom: 20px;
          
          .search-input {
            max-width: 500px;
          }
        }
        
        .filter-options {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 16px;
          
          .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
            
            label {
              font-weight: 500;
              color: var(--el-text-color-primary);
              white-space: nowrap;
            }
            
            .filter-select {
              width: 140px;
            }
            
            .option-icon {
              margin-right: 6px;
            }
          }
        }
        
        .quick-filters {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .quick-filter-tag {
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
  
  .request-list {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .list-info {
        display: flex;
        align-items: center;
        color: var(--el-text-color-regular);
        font-size: 14px;
        
        .total-count {
          font-weight: 500;
        }
      }
    }
    
    .loading-state {
      padding: 20px 0;
    }
    
    .empty-state {
      padding: 60px 0;
      text-align: center;
    }
    
    .request-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 20px;
      
      &.list-view {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
    
    .load-more {
      text-align: center;
      margin-top: 32px;
      
      .load-more-btn {
        padding: 12px 32px;
      }
      
      .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: var(--el-text-color-regular);
        
        .el-icon {
          font-size: 16px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .request-square {
    padding: 12px;
    
    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-content h1 {
        font-size: 24px;
      }
    }
    
    .filter-options {
      flex-direction: column;
      align-items: stretch;
      
      .filter-group {
        justify-content: space-between;
        
        .filter-select {
          width: 200px;
        }
      }
    }
    
    .list-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
    
    .request-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}
</style>
