<template>
  <div class="home">
    <!-- 导航栏 -->
    <nav-bar />
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 欢迎横幅 -->
      <div class="welcome-banner">
        <el-card class="banner-card">
          <div class="banner-content">
            <div class="banner-text">
              <h1>欢迎来到校园搭子</h1>
              <p>找到志同道合的伙伴，让校园生活更精彩</p>
            </div>
            <div class="banner-actions">
              <el-button type="primary" size="large" @click="goToPublish">
                <el-icon><Plus /></el-icon>
                发布需求
              </el-button>
              <el-button size="large" @click="goToSquare">
                <el-icon><Search /></el-icon>
                浏览需求
              </el-button>
              <el-button size="large" @click="goToMatchCenter">
                <el-icon><Connection /></el-icon>
                匹配中心
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 快速入口 -->
      <div class="quick-access">
        <h2>快速入口</h2>
        <div class="access-grid">
          <div class="access-item" @click="goToSquare">
            <el-icon class="access-icon"><Grid /></el-icon>
            <h3>需求广场</h3>
            <p>浏览所有需求</p>
          </div>
          
          <div class="access-item" @click="goToPublish">
            <el-icon class="access-icon"><EditPen /></el-icon>
            <h3>发布需求</h3>
            <p>发布你的需求</p>
          </div>
          
          <div class="access-item" @click="goToMatchCenter">
            <el-icon class="access-icon"><Connection /></el-icon>
            <h3>匹配中心</h3>
            <p>智能匹配搭子</p>
          </div>

          <div class="access-item" @click="goToVerification">
            <el-icon class="access-icon"><UserFilled /></el-icon>
            <h3>身份认证</h3>
            <p>学号和人脸认证</p>
          </div>

          <div class="access-item" @click="goToEvaluation">
            <el-icon class="access-icon"><Star /></el-icon>
            <h3>评价中心</h3>
            <p>查看和管理评价</p>
          </div>

          <div class="access-item" @click="goToMessages">
            <el-icon class="access-icon"><ChatDotRound /></el-icon>
            <h3>消息中心</h3>
            <p>查看聊天消息</p>
          </div>
        </div>
      </div>

      <!-- 热门需求 -->
      <div class="hot-requests">
        <div class="section-header">
          <h2>热门需求</h2>
          <el-button type="text" @click="goToSquare">查看更多</el-button>
        </div>
        
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="hotRequests.length > 0" class="requests-grid">
          <request-card
            v-for="request in hotRequests"
            :key="request.id"
            :request="request"
            @update="fetchHotRequests"
            class="request-item"
          />
        </div>
        
        <div v-else class="empty-state">
          <el-empty description="暂无热门需求">
            <el-button type="primary" @click="goToPublish">发布第一个需求</el-button>
          </el-empty>
        </div>
      </div>

      <!-- 推荐需求 -->
      <div class="recommended-requests">
        <div class="section-header">
          <h2>为你推荐</h2>
          <el-button type="text" @click="goToSquare">查看更多</el-button>
        </div>
        
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="recommendedRequests.length > 0" class="requests-grid">
          <request-card
            v-for="request in recommendedRequests"
            :key="request.id"
            :request="request"
            @update="fetchRecommendedRequests"
            class="request-item"
          />
        </div>
        
        <div v-else class="empty-state">
          <el-empty description="暂无推荐需求">
            <el-button type="primary" @click="goToSquare">去需求广场看看</el-button>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBuddyRequestStore } from '@/stores/buddy-request'
import NavBar from '@/components/layout/NavBar.vue'
import RequestCard from '@/components/request/RequestCard.vue'
import {
  Plus,
  Search,
  Grid,
  EditPen,
  Connection,
  ChatDotRound,
  UserFilled,
  Star
} from '@element-plus/icons-vue'

const router = useRouter()
const buddyRequestStore = useBuddyRequestStore()

// 响应式数据
const loading = ref(false)
const hotRequests = ref([])
const recommendedRequests = ref([])

// 方法
const goToPublish = () => {
  router.push('/publish')
}

const goToSquare = () => {
  router.push('/square')
}

const goToMatchCenter = () => {
  router.push('/match-center')
}

const goToVerification = () => {
  router.push('/verification')
}

const goToEvaluation = () => {
  router.push('/evaluation')
}

const goToMessages = () => {
  router.push('/messages')
}

const fetchHotRequests = async () => {
  loading.value = true
  try {
    const response = await buddyRequestStore.fetchHotRequests({ page: 1, size: 6 })
    if (response && response.success) {
      hotRequests.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取热门需求失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchRecommendedRequests = async () => {
  loading.value = true
  try {
    const response = await buddyRequestStore.fetchRecommendedRequests({ page: 1, size: 6 })
    if (response && response.success) {
      recommendedRequests.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取推荐需求失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(async () => {
  await Promise.all([
    fetchHotRequests(),
    fetchRecommendedRequests()
  ])
})
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-banner {
  margin-bottom: 40px;
  
  .banner-card {
    background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
    border: none;
    
    .banner-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: white;
      
      .banner-text {
        h1 {
          font-size: 32px;
          font-weight: 600;
          margin-bottom: 12px;
        }
        
        p {
          font-size: 18px;
          opacity: 0.9;
        }
      }
      
      .banner-actions {
        display: flex;
        gap: 16px;
        
        .el-button {
          padding: 12px 24px;
          font-size: 16px;
          
          .el-icon {
            margin-right: 6px;
          }
        }
      }
    }
  }
}

.quick-access {
  margin-bottom: 40px;
  
  h2 {
    margin-bottom: 20px;
    color: var(--el-text-color-primary);
  }
  
  .access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    
    .access-item {
      padding: 24px;
      background: var(--el-bg-color);
      border-radius: 12px;
      box-shadow: var(--el-box-shadow-light);
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--el-box-shadow);
      }
      
      .access-icon {
        font-size: 48px;
        color: var(--el-color-primary);
        margin-bottom: 16px;
      }
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
      }
      
      p {
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
  }
}

.hot-requests,
.recommended-requests {
  margin-bottom: 40px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      color: var(--el-text-color-primary);
    }
  }
  
  .loading-container {
    padding: 20px 0;
  }
  
  .requests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }
  
  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 12px;
  }
  
  .welcome-banner {
    .banner-content {
      flex-direction: column;
      text-align: center;
      gap: 20px;
      
      .banner-actions {
        justify-content: center;
      }
    }
  }
  
  .access-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .requests-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
