<template>
  <div class="evaluation-container">
    <div class="evaluation-card">
      <div class="card-header">
        <h2>搭子评价</h2>
        <p>请对本次搭子体验进行评价，您的评价将帮助其他同学更好地选择搭子</p>
      </div>

      <!-- 搭子信息 -->
      <div class="buddy-info">
        <el-avatar :size="60" :src="buddyInfo.avatar" />
        <div class="info-content">
          <h3>{{ buddyInfo.nickname }}</h3>
          <p>{{ buddyInfo.college }} · {{ buddyInfo.grade }}</p>
          <div class="activity-info">
            <el-tag size="small">{{ buddyInfo.activityType }}</el-tag>
            <span class="activity-time">{{ buddyInfo.activityTime }}</span>
          </div>
        </div>
      </div>

      <!-- 评价表单 -->
      <el-form
        ref="formRef"
        :model="evaluationForm"
        :rules="rules"
        label-width="120px"
        class="evaluation-form"
      >
        <!-- 总体评分 -->
        <el-form-item label="总体评分" prop="overallRating">
          <el-rate
            v-model="evaluationForm.overallRating"
            :max="5"
            show-text
            :texts="ratingTexts"
            size="large"
          />
        </el-form-item>

        <!-- 详细评分 -->
        <div class="detailed-ratings">
          <h3>详细评分</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="准时性" prop="punctualityRating">
                <el-rate
                  v-model="evaluationForm.punctualityRating"
                  :max="5"
                  size="small"
                />
                <span class="rating-label">{{ getRatingLabel(evaluationForm.punctualityRating) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="沟通能力" prop="communicationRating">
                <el-rate
                  v-model="evaluationForm.communicationRating"
                  :max="5"
                  size="small"
                />
                <span class="rating-label">{{ getRatingLabel(evaluationForm.communicationRating) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="合作能力" prop="cooperationRating">
                <el-rate
                  v-model="evaluationForm.cooperationRating"
                  :max="5"
                  size="small"
                />
                <span class="rating-label">{{ getRatingLabel(evaluationForm.cooperationRating) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 标签评价 -->
        <el-form-item label="标签评价" prop="evaluationTags">
          <div class="tags-section">
            <h4>请选择1-3个最符合的标签</h4>
            <div class="tags-grid">
              <div
                v-for="category in tagCategories"
                :key="category.name"
                class="tag-category"
              >
                <h5>{{ category.name }}</h5>
                <div class="tags-list">
                  <el-tag
                    v-for="tag in category.tags"
                    :key="tag.id"
                    :type="getTagType(category.type)"
                    :effect="selectedTags.includes(tag.id) ? 'dark' : 'plain'"
                    class="evaluation-tag"
                    @click="toggleTag(tag.id)"
                  >
                    <el-icon>
                      <component :is="tag.icon" />
                    </el-icon>
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="selected-tags">
              <span>已选择: {{ selectedTags.length }}/3</span>
            </div>
          </div>
        </el-form-item>

        <!-- 文字评价 -->
        <el-form-item label="文字评价" prop="evaluationText">
          <el-input
            v-model="evaluationForm.evaluationText"
            type="textarea"
            :rows="4"
            placeholder="请简要描述本次搭子体验（选填，100字以内）"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 匿名选项 -->
        <el-form-item label="评价方式">
          <el-radio-group v-model="evaluationForm.isAnonymous">
            <el-radio :label="true">匿名评价（推荐）</el-radio>
            <el-radio :label="false">实名评价</el-radio>
          </el-radio-group>
          <div class="anonymous-tip">
            <el-icon><InfoFilled /></el-icon>
            匿名评价不会显示您的身份信息，但评价内容会展示给其他用户参考
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <el-button @click="goBack">取消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="submitEvaluation"
          >
            {{ submitting ? '提交中...' : '提交评价' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 评价说明 -->
    <div class="evaluation-tips">
      <h3>评价说明</h3>
      <ul>
        <li>您的评价将帮助其他同学更好地了解和选择搭子</li>
        <li>请客观公正地进行评价，避免恶意差评或虚假好评</li>
        <li>匿名评价不会显示您的身份，但系统会记录用于信用体系</li>
        <li>优质的评价者在匹配时会获得更高的优先级</li>
        <li>评价一旦提交无法修改，请谨慎填写</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled, Star, Trophy, ChatDotRound, UserFilled, Clock } from '@element-plus/icons-vue'
import { submitUserEvaluation, getEvaluationTags } from '@/api/evaluation'

const route = useRoute()
const router = useRouter()
const formRef = ref()
const submitting = ref(false)
const selectedTags = ref([])

// 搭子信息
const buddyInfo = reactive({
  id: route.params.id,
  nickname: '学习小能手',
  avatar: '/avatars/user1.jpg',
  college: '计算机学院',
  grade: '2022级',
  activityType: '图书馆学习',
  activityTime: '2024-01-15 14:00-17:00'
})

// 评价表单
const evaluationForm = reactive({
  overallRating: 0,
  punctualityRating: 0,
  communicationRating: 0,
  cooperationRating: 0,
  evaluationTags: [],
  evaluationText: '',
  isAnonymous: true
})

// 评分文本
const ratingTexts = ['很差', '较差', '一般', '很好', '非常好']

// 标签分类
const tagCategories = ref([
  {
    name: '正向标签',
    type: 'positive',
    tags: [
      { id: 1, name: '准时王者', icon: 'Clock' },
      { id: 2, name: '话题达人', icon: 'ChatDotRound' },
      { id: 3, name: '学习专注', icon: 'Star' },
      { id: 4, name: '干饭神速', icon: 'Trophy' },
      { id: 5, name: '运动健将', icon: 'Trophy' },
      { id: 6, name: '贴心暖人', icon: 'UserFilled' }
    ]
  },
  {
    name: '中性标签',
    type: 'neutral',
    tags: [
      { id: 7, name: '安静内向', icon: 'UserFilled' },
      { id: 8, name: '活泼外向', icon: 'UserFilled' },
      { id: 9, name: '慢热型', icon: 'UserFilled' },
      { id: 10, name: '效率至上', icon: 'Star' }
    ]
  }
])

// 表单验证规则
const rules = {
  overallRating: [
    { required: true, message: '请给出总体评分', trigger: 'change' }
  ],
  punctualityRating: [
    { required: true, message: '请评价准时性', trigger: 'change' }
  ],
  communicationRating: [
    { required: true, message: '请评价沟通能力', trigger: 'change' }
  ],
  cooperationRating: [
    { required: true, message: '请评价合作能力', trigger: 'change' }
  ]
}

// 计算属性
const canSelectMoreTags = computed(() => selectedTags.value.length < 3)

// 方法
const getRatingLabel = (rating) => {
  return ratingTexts[rating - 1] || '未评分'
}

const getTagType = (categoryType) => {
  const types = {
    positive: 'success',
    neutral: 'info',
    negative: 'warning'
  }
  return types[categoryType] || 'info'
}

const toggleTag = (tagId) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else if (canSelectMoreTags.value) {
    selectedTags.value.push(tagId)
  } else {
    ElMessage.warning('最多只能选择3个标签')
  }
  evaluationForm.evaluationTags = selectedTags.value
}

const submitEvaluation = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (selectedTags.value.length === 0) {
      ElMessage.warning('请至少选择一个标签')
      return
    }

    await ElMessageBox.confirm(
      '确定提交评价吗？评价提交后无法修改',
      '确认提交',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '再想想',
        type: 'info'
      }
    )

    submitting.value = true

    const evaluationData = {
      ...evaluationForm,
      matchRecordId: route.params.id,
      evaluatedUserId: buddyInfo.id,
      evaluationTags: selectedTags.value
    }

    const response = await submitUserEvaluation(evaluationData)

    if (response.success) {
      ElMessage.success('评价提交成功！')
      router.push('/match-center')
    } else {
      ElMessage.error(response.message || '评价提交失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('评价提交失败:', error)
      ElMessage.error('评价提交失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

const goBack = () => {
  router.back()
}

onMounted(async () => {
  // 加载评价标签
  try {
    const response = await getEvaluationTags()
    if (response.success) {
      // 更新标签分类数据
    }
  } catch (error) {
    console.error('加载评价标签失败:', error)
  }
})
</script>

<style scoped>
.evaluation-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.evaluation-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  text-align: center;
  margin-bottom: 30px;
}

.card-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.card-header p {
  color: #7f8c8d;
  font-size: 14px;
}

.buddy-info {
  display: flex;
  align-items: center;
  gap: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.info-content h3 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.info-content p {
  margin: 0 0 10px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.activity-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.activity-time {
  color: #7f8c8d;
  font-size: 12px;
}

.evaluation-form {
  max-width: 600px;
  margin: 0 auto;
}

.detailed-ratings {
  margin: 30px 0;
}

.detailed-ratings h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 16px;
}

.rating-label {
  margin-left: 10px;
  color: #7f8c8d;
  font-size: 12px;
}

.tags-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 14px;
}

.tags-grid {
  margin-bottom: 15px;
}

.tag-category {
  margin-bottom: 20px;
}

.tag-category h5 {
  color: #5a6c7d;
  margin-bottom: 10px;
  font-size: 13px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.evaluation-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.evaluation-tag:hover {
  transform: translateY(-1px);
}

.selected-tags {
  text-align: right;
  color: #7f8c8d;
  font-size: 12px;
}

.anonymous-tip {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #7f8c8d;
  font-size: 12px;
  margin-top: 8px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}

.evaluation-tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.evaluation-tips h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.evaluation-tips ul {
  list-style: none;
  padding: 0;
}

.evaluation-tips li {
  color: #5a6c7d;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.evaluation-tips li::before {
  content: '•';
  color: #3498db;
  position: absolute;
  left: 0;
}
</style>
