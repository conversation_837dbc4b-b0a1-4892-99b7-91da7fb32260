<template>
  <div class="match-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>缘分池匹配中心</h1>
      <p>智能匹配，遇见最合适的校园搭子</p>
    </div>

    <!-- 匹配状态卡片 -->
    <div class="match-status-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="status-card">
            <div class="status-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="status-info">
              <h3>{{ matchStats.pending }}</h3>
              <p>待匹配需求</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-card">
            <div class="status-icon matched">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="status-info">
              <h3>{{ matchStats.matched }}</h3>
              <p>今日匹配</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-card">
            <div class="status-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="status-info">
              <h3>{{ matchStats.success }}</h3>
              <p>成功配对</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-card">
            <div class="status-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="status-info">
              <h3>{{ matchStats.successRate }}%</h3>
              <p>匹配成功率</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 匹配进度 -->
    <div v-if="matchingInProgress" class="matching-progress">
      <el-card>
        <div class="progress-content">
          <div class="progress-icon">
            <el-icon class="rotating"><Loading /></el-icon>
          </div>
          <div class="progress-info">
            <h3>正在为您匹配搭子...</h3>
            <p>{{ matchingStatus }}</p>
            <el-progress :percentage="matchingProgress" :show-text="false" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 匹配结果 -->
    <div v-if="matchResults.length > 0" class="match-results">
      <h2>为您找到的潜在搭子</h2>
      <el-row :gutter="20">
        <el-col
          v-for="match in matchResults"
          :key="match.id"
          :span="8"
        >
          <div class="match-card">
            <div class="match-header">
              <el-avatar :size="60" :src="match.avatar" />
              <div class="match-info">
                <h3>{{ match.nickname }}</h3>
                <p>{{ match.college }} · {{ match.grade }}</p>
                <div class="match-score">
                  匹配度: <span class="score">{{ match.matchScore }}%</span>
                </div>
              </div>
            </div>

            <div class="match-details">
              <div class="match-reason">
                <h4>匹配原因</h4>
                <el-tag
                  v-for="reason in match.matchReasons"
                  :key="reason"
                  size="small"
                  class="reason-tag"
                >
                  {{ reason }}
                </el-tag>
              </div>

              <div class="match-tags">
                <h4>个人标签</h4>
                <el-tag
                  v-for="tag in match.tags"
                  :key="tag"
                  type="info"
                  size="small"
                  class="user-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <div class="match-evaluation">
                <h4>搭子评价</h4>
                <div class="evaluation-stats">
                  <span>准时王者 × {{ match.evaluations.punctual }}</span>
                  <span>话题达人 × {{ match.evaluations.talkative }}</span>
                  <span>学习专注 × {{ match.evaluations.focused }}</span>
                </div>
              </div>
            </div>

            <div class="match-actions">
              <el-button
                size="small"
                @click="viewProfile(match.id)"
              >
                查看详情
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="rejectMatch(match.id)"
              >
                不感兴趣
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="acceptMatch(match.id)"
              >
                接受匹配
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 我的匹配记录 -->
    <div class="match-history">
      <div class="section-header">
        <h2>我的匹配记录</h2>
        <el-button
          type="primary"
          @click="triggerManualMatch"
          :loading="manualMatching"
        >
          {{ manualMatching ? '匹配中...' : '手动匹配' }}
        </el-button>
      </div>

      <el-table :data="matchHistory" style="width: 100%">
        <el-table-column prop="requestTitle" label="需求标题" />
        <el-table-column prop="matchedUser" label="匹配搭子" />
        <el-table-column prop="matchScore" label="匹配度">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.matchScore"
              :show-text="false"
              :stroke-width="8"
            />
            <span class="score-text">{{ scope.row.matchScore }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="匹配时间" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 'pending'"
              type="primary"
              size="small"
              @click="handleMatch(scope.row)"
            >
              处理
            </el-button>
            <el-button
              v-if="scope.row.status === 'accepted'"
              type="success"
              size="small"
              @click="startActivity(scope.row)"
            >
              开始活动
            </el-button>
            <el-button
              v-if="scope.row.status === 'completed'"
              size="small"
              @click="evaluateMatch(scope.row)"
            >
              评价
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 匹配设置 -->
    <div class="match-settings">
      <h2>匹配偏好设置</h2>
      <el-form :model="matchPreferences" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先校区">
              <el-select v-model="matchPreferences.preferredCampus" multiple>
                <el-option label="主校区" value="main" />
                <el-option label="东校区" value="east" />
                <el-option label="西校区" value="west" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="匹配时间">
              <el-select v-model="matchPreferences.preferredTime">
                <el-option label="工作日" value="weekday" />
                <el-option label="周末" value="weekend" />
                <el-option label="任意时间" value="anytime" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别偏好">
              <el-radio-group v-model="matchPreferences.genderPreference">
                <el-radio label="any">不限</el-radio>
                <el-radio label="same">同性</el-radio>
                <el-radio label="opposite">异性</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年级偏好">
              <el-select v-model="matchPreferences.gradePreference" multiple>
                <el-option label="同年级" value="same" />
                <el-option label="学长学姐" value="senior" />
                <el-option label="学弟学妹" value="junior" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="savePreferences">保存设置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock,
  UserFilled,
  SuccessFilled,
  TrendCharts,
  Loading
} from '@element-plus/icons-vue'

// 响应式数据
const matchStats = reactive({
  pending: 12,
  matched: 8,
  success: 6,
  successRate: 75
})

const matchingInProgress = ref(false)
const matchingStatus = ref('')
const matchingProgress = ref(0)
const manualMatching = ref(false)

const matchResults = ref([
  {
    id: 1,
    nickname: '学习小能手',
    avatar: '/avatars/user1.jpg',
    college: '计算机学院',
    grade: '2022级',
    matchScore: 92,
    matchReasons: ['同专业', '学习时间相近', '图书馆常客'],
    tags: ['学霸', '安静', '专注'],
    evaluations: {
      punctual: 5,
      talkative: 3,
      focused: 8
    }
  },
  {
    id: 2,
    nickname: '运动达人',
    avatar: '/avatars/user2.jpg',
    college: '体育学院',
    grade: '2023级',
    matchScore: 88,
    matchReasons: ['运动爱好相同', '时间匹配', '活跃度高'],
    tags: ['活力', '健康', '阳光'],
    evaluations: {
      punctual: 7,
      talkative: 6,
      focused: 4
    }
  }
])

const matchHistory = ref([
  {
    id: 1,
    requestTitle: '图书馆学习搭子',
    matchedUser: '小明同学',
    matchScore: 85,
    status: 'completed',
    createdAt: '2024-01-15 14:30'
  },
  {
    id: 2,
    requestTitle: '食堂饭友',
    matchedUser: '小红同学',
    matchScore: 78,
    status: 'accepted',
    createdAt: '2024-01-14 12:00'
  }
])

const matchPreferences = reactive({
  preferredCampus: ['main'],
  preferredTime: 'anytime',
  genderPreference: 'any',
  gradePreference: ['same']
})

// 方法
const triggerManualMatch = async () => {
  manualMatching.value = true
  matchingInProgress.value = true
  matchingStatus.value = '正在分析您的需求...'
  matchingProgress.value = 0

  // 模拟匹配过程
  const steps = [
    { status: '正在分析您的需求...', progress: 20 },
    { status: '正在搜索潜在搭子...', progress: 50 },
    { status: '正在计算匹配度...', progress: 80 },
    { status: '匹配完成！', progress: 100 }
  ]

  for (const step of steps) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    matchingStatus.value = step.status
    matchingProgress.value = step.progress
  }

  setTimeout(() => {
    matchingInProgress.value = false
    manualMatching.value = false
    ElMessage.success('匹配完成，为您找到了2个潜在搭子！')
  }, 500)
}

const acceptMatch = async (matchId) => {
  try {
    await ElMessageBox.confirm('确定接受这个匹配吗？', '确认匹配', {
      confirmButtonText: '接受',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    // 这里调用接受匹配的API
    ElMessage.success('匹配接受成功！系统将通知对方')
  } catch {
    // 用户取消
  }
}

const rejectMatch = async (matchId) => {
  try {
    await ElMessageBox.confirm('确定拒绝这个匹配吗？系统将为您寻找其他搭子', '拒绝匹配', {
      confirmButtonText: '拒绝',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里调用拒绝匹配的API
    ElMessage.info('已拒绝匹配，系统将为您重新匹配')
  } catch {
    // 用户取消
  }
}

const viewProfile = (userId) => {
  // 跳转到用户详情页面
  console.log('查看用户详情:', userId)
}

const handleMatch = (match) => {
  // 处理匹配请求
  console.log('处理匹配:', match)
}

const startActivity = (match) => {
  // 开始活动
  console.log('开始活动:', match)
}

const evaluateMatch = (match) => {
  // 跳转到评价页面
  router.push(`/evaluation/${match.id}`)
}

const savePreferences = () => {
  // 保存匹配偏好
  ElMessage.success('匹配偏好已保存')
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    accepted: 'success',
    rejected: 'danger',
    completed: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待确认',
    accepted: '已接受',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return texts[status] || '未知'
}

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.match-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.match-status-section {
  margin-bottom: 30px;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.status-icon.pending { background: #f39c12; }
.status-icon.matched { background: #3498db; }
.status-icon.success { background: #27ae60; }
.status-icon.rate { background: #9b59b6; }

.status-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #2c3e50;
}

.status-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.matching-progress {
  margin-bottom: 30px;
}

.progress-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.progress-icon {
  font-size: 40px;
  color: #409eff;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-info {
  flex: 1;
}

.progress-info h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.progress-info p {
  margin: 0 0 15px 0;
  color: #7f8c8d;
}

.match-results {
  margin-bottom: 40px;
}

.match-results h2 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.match-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.match-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.match-info h3 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.match-info p {
  margin: 0 0 8px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.match-score {
  font-size: 14px;
  color: #7f8c8d;
}

.score {
  color: #27ae60;
  font-weight: bold;
}

.match-details {
  margin-bottom: 20px;
}

.match-details h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
}

.reason-tag, .user-tag {
  margin: 0 5px 5px 0;
}

.evaluation-stats {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #7f8c8d;
}

.match-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.match-history {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  color: #2c3e50;
  margin: 0;
}

.score-text {
  margin-left: 10px;
  font-size: 12px;
  color: #7f8c8d;
}

.match-settings {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.match-settings h2 {
  color: #2c3e50;
  margin-bottom: 20px;
}
</style>
