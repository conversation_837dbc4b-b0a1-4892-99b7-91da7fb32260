<template>
  <div class="verification-container">
    <div class="verification-card">
      <div class="card-header">
        <h2>学号认证</h2>
        <p>请输入您的学号和校园网密码进行身份验证</p>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="verification-form"
      >
        <el-form-item label="学号" prop="studentId">
          <el-input
            v-model="form.studentId"
            placeholder="请输入学号"
            maxlength="12"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="校园网密码" prop="campusPassword">
          <el-input
            v-model="form.campusPassword"
            type="password"
            placeholder="请输入校园网密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="form.realName"
            placeholder="请输入真实姓名"
          />
        </el-form-item>

        <el-form-item label="学院" prop="college">
          <el-select
            v-model="form.college"
            placeholder="请选择学院"
            style="width: 100%"
          >
            <el-option
              v-for="college in colleges"
              :key="college.value"
              :label="college.label"
              :value="college.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="专业" prop="major">
          <el-input
            v-model="form.major"
            placeholder="请输入专业"
          />
        </el-form-item>

        <el-form-item label="年级" prop="grade">
          <el-select
            v-model="form.grade"
            placeholder="请选择年级"
            style="width: 100%"
          >
            <el-option label="2024级" value="2024" />
            <el-option label="2023级" value="2023" />
            <el-option label="2022级" value="2022" />
            <el-option label="2021级" value="2021" />
            <el-option label="2020级" value="2020" />
          </el-select>
        </el-form-item>

        <div class="form-actions">
          <el-button @click="resetForm">重置</el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="submitForm"
          >
            {{ loading ? '认证中...' : '开始认证' }}
          </el-button>
        </div>
      </el-form>

      <!-- 认证进度 -->
      <div v-if="verificationStep > 0" class="verification-progress">
        <el-steps :active="verificationStep" finish-status="success">
          <el-step title="提交信息" />
          <el-step title="校验身份" />
          <el-step title="认证完成" />
        </el-steps>
      </div>

      <!-- 认证结果 -->
      <div v-if="verificationResult" class="verification-result">
        <el-result
          :icon="verificationResult.success ? 'success' : 'error'"
          :title="verificationResult.title"
          :sub-title="verificationResult.message"
        >
          <template #extra>
            <el-button
              v-if="verificationResult.success"
              type="primary"
              @click="goToProfile"
            >
              完善个人信息
            </el-button>
            <el-button
              v-else
              @click="resetVerification"
            >
              重新认证
            </el-button>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 认证说明 -->
    <div class="verification-tips">
      <h3>认证说明</h3>
      <ul>
        <li>学号认证是使用系统的必要步骤，确保用户身份真实可靠</li>
        <li>我们将通过学校统一身份认证系统验证您的身份</li>
        <li>您的校园网密码仅用于身份验证，不会被存储</li>
        <li>认证成功后，您可以享受完整的校园搭子服务</li>
        <li>如遇问题，请联系客服或学校网络中心</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { verifyStudentId } from '@/api/verification'

const router = useRouter()
const formRef = ref()
const loading = ref(false)
const verificationStep = ref(0)
const verificationResult = ref(null)

// 表单数据
const form = reactive({
  studentId: '',
  campusPassword: '',
  realName: '',
  college: '',
  major: '',
  grade: ''
})

// 学院列表
const colleges = [
  { label: '计算机科学与技术学院', value: '计算机科学与技术学院' },
  { label: '软件学院', value: '软件学院' },
  { label: '信息工程学院', value: '信息工程学院' },
  { label: '经济管理学院', value: '经济管理学院' },
  { label: '外国语学院', value: '外国语学院' },
  { label: '艺术设计学院', value: '艺术设计学院' }
]

// 表单验证规则
const rules = {
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { pattern: /^[0-9]{8,12}$/, message: '学号格式不正确', trigger: 'blur' }
  ],
  campusPassword: [
    { required: true, message: '请输入校园网密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  college: [
    { required: true, message: '请选择学院', trigger: 'change' }
  ],
  major: [
    { required: true, message: '请输入专业', trigger: 'blur' }
  ],
  grade: [
    { required: true, message: '请选择年级', trigger: 'change' }
  ]
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    verificationStep.value = 1

    // 调用认证API
    const response = await verifyStudentId(form)
    
    verificationStep.value = 2
    
    if (response.success) {
      verificationStep.value = 3
      verificationResult.value = {
        success: true,
        title: '认证成功！',
        message: '您的学号认证已通过，现在可以使用完整的校园搭子服务'
      }
      ElMessage.success('学号认证成功')
    } else {
      verificationResult.value = {
        success: false,
        title: '认证失败',
        message: response.message || '认证过程中出现错误，请检查信息后重试'
      }
      ElMessage.error(response.message || '认证失败')
    }
  } catch (error) {
    console.error('认证失败:', error)
    verificationResult.value = {
      success: false,
      title: '认证失败',
      message: '网络错误或服务暂时不可用，请稍后重试'
    }
    ElMessage.error('认证失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}

// 重置认证
const resetVerification = () => {
  verificationStep.value = 0
  verificationResult.value = null
  resetForm()
}

// 跳转到个人资料页面
const goToProfile = () => {
  router.push('/profile')
}
</script>

<style scoped>
.verification-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.verification-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  text-align: center;
  margin-bottom: 30px;
}

.card-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.card-header p {
  color: #7f8c8d;
  font-size: 14px;
}

.verification-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}

.verification-progress {
  margin: 30px 0;
}

.verification-result {
  margin-top: 30px;
}

.verification-tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.verification-tips h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.verification-tips ul {
  list-style: none;
  padding: 0;
}

.verification-tips li {
  color: #5a6c7d;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.verification-tips li::before {
  content: '•';
  color: #3498db;
  position: absolute;
  left: 0;
}
</style>
