<template>
  <div class="face-verification-container">
    <div class="verification-card">
      <div class="card-header">
        <h2>人脸认证</h2>
        <p>请正对摄像头，确保光线充足，面部清晰可见</p>
      </div>

      <!-- 摄像头区域 -->
      <div class="camera-section">
        <div class="camera-container">
          <video
            ref="videoRef"
            :class="{ 'camera-active': cameraActive }"
            autoplay
            muted
            playsinline
          ></video>
          <canvas ref="canvasRef" style="display: none;"></canvas>
          
          <!-- 人脸框指引 -->
          <div class="face-guide" :class="{ 'face-detected': faceDetected }">
            <div class="guide-circle">
              <div class="guide-text">
                {{ faceDetected ? '检测到人脸' : '请将面部对准圆圈' }}
              </div>
            </div>
          </div>

          <!-- 拍照按钮 -->
          <div class="camera-controls">
            <el-button
              v-if="!cameraActive"
              type="primary"
              size="large"
              @click="startCamera"
            >
              <el-icon><Camera /></el-icon>
              开启摄像头
            </el-button>
            
            <div v-else class="control-buttons">
              <el-button
                type="success"
                size="large"
                :disabled="!faceDetected || capturing"
                @click="capturePhoto"
              >
                <el-icon><Picture /></el-icon>
                {{ capturing ? '拍摄中...' : '拍照认证' }}
              </el-button>
              
              <el-button
                size="large"
                @click="stopCamera"
              >
                <el-icon><Close /></el-icon>
                关闭摄像头
              </el-button>
            </div>
          </div>
        </div>

        <!-- 拍摄的照片预览 -->
        <div v-if="capturedImage" class="photo-preview">
          <h3>拍摄的照片</h3>
          <img :src="capturedImage" alt="拍摄的照片" />
          <div class="preview-actions">
            <el-button @click="retakePhoto">重新拍摄</el-button>
            <el-button
              type="primary"
              :loading="verifying"
              @click="submitVerification"
            >
              {{ verifying ? '认证中...' : '提交认证' }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 认证进度 -->
      <div v-if="verificationStep > 0" class="verification-progress">
        <el-steps :active="verificationStep" finish-status="success">
          <el-step title="拍摄照片" />
          <el-step title="人脸比对" />
          <el-step title="认证完成" />
        </el-steps>
      </div>

      <!-- 认证结果 -->
      <div v-if="verificationResult" class="verification-result">
        <el-result
          :icon="verificationResult.success ? 'success' : 'error'"
          :title="verificationResult.title"
          :sub-title="verificationResult.message"
        >
          <template #extra>
            <el-button
              v-if="verificationResult.success"
              type="primary"
              @click="goBack"
            >
              继续操作
            </el-button>
            <el-button
              v-else
              @click="resetVerification"
            >
              重新认证
            </el-button>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 认证提示 -->
    <div class="verification-tips">
      <h3>认证提示</h3>
      <div class="tips-grid">
        <div class="tip-item">
          <el-icon class="tip-icon"><Sunny /></el-icon>
          <div>
            <h4>光线充足</h4>
            <p>确保面部光线均匀，避免逆光或阴影</p>
          </div>
        </div>
        <div class="tip-item">
          <el-icon class="tip-icon"><User /></el-icon>
          <div>
            <h4>正面拍摄</h4>
            <p>请正对摄像头，保持面部完整清晰</p>
          </div>
        </div>
        <div class="tip-item">
          <el-icon class="tip-icon"><View /></el-icon>
          <div>
            <h4>摘除遮挡</h4>
            <p>请摘除帽子、墨镜等遮挡面部的物品</p>
          </div>
        </div>
        <div class="tip-item">
          <el-icon class="tip-icon"><Lock /></el-icon>
          <div>
            <h4>隐私保护</h4>
            <p>照片仅用于身份验证，不会被存储或泄露</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Camera, Picture, Close, Sunny, User, View, Lock } from '@element-plus/icons-vue'
import { verifyFace } from '@/api/verification'

const videoRef = ref()
const canvasRef = ref()
const cameraActive = ref(false)
const faceDetected = ref(false)
const capturing = ref(false)
const verifying = ref(false)
const capturedImage = ref('')
const verificationStep = ref(0)
const verificationResult = ref(null)

let mediaStream = null
let faceDetectionInterval = null

// 启动摄像头
const startCamera = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: 640 },
        height: { ideal: 480 },
        facingMode: 'user'
      }
    })
    
    if (videoRef.value) {
      videoRef.value.srcObject = mediaStream
      cameraActive.value = true
      startFaceDetection()
    }
  } catch (error) {
    console.error('摄像头启动失败:', error)
    ElMessage.error('无法访问摄像头，请检查权限设置')
  }
}

// 停止摄像头
const stopCamera = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach(track => track.stop())
    mediaStream = null
  }
  cameraActive.value = false
  faceDetected.value = false
  stopFaceDetection()
}

// 开始人脸检测（简化版本）
const startFaceDetection = () => {
  faceDetectionInterval = setInterval(() => {
    // 这里应该集成真实的人脸检测库，如 face-api.js
    // 为了演示，我们随机模拟检测结果
    faceDetected.value = Math.random() > 0.3
  }, 1000)
}

// 停止人脸检测
const stopFaceDetection = () => {
  if (faceDetectionInterval) {
    clearInterval(faceDetectionInterval)
    faceDetectionInterval = null
  }
}

// 拍摄照片
const capturePhoto = () => {
  if (!videoRef.value || !canvasRef.value) return
  
  capturing.value = true
  
  const canvas = canvasRef.value
  const video = videoRef.value
  
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight
  
  const ctx = canvas.getContext('2d')
  ctx.drawImage(video, 0, 0)
  
  capturedImage.value = canvas.toDataURL('image/jpeg', 0.8)
  capturing.value = false
  
  stopCamera()
}

// 重新拍摄
const retakePhoto = () => {
  capturedImage.value = ''
  verificationStep.value = 0
  verificationResult.value = null
  startCamera()
}

// 提交认证
const submitVerification = async () => {
  if (!capturedImage.value) return
  
  try {
    verifying.value = true
    verificationStep.value = 1
    
    // 将图片转换为base64格式
    const base64Data = capturedImage.value.split(',')[1]
    
    const response = await verifyFace({
      faceImageBase64: base64Data,
      operationType: 'general_verification',
      deviceInfo: navigator.userAgent
    })
    
    verificationStep.value = 2
    
    if (response.success) {
      verificationStep.value = 3
      verificationResult.value = {
        success: true,
        title: '人脸认证成功！',
        message: `比对分数: ${response.data.score}%，身份验证通过`
      }
      ElMessage.success('人脸认证成功')
    } else {
      verificationResult.value = {
        success: false,
        title: '人脸认证失败',
        message: response.message || '人脸比对未通过，请重新拍摄'
      }
      ElMessage.error(response.message || '人脸认证失败')
    }
  } catch (error) {
    console.error('人脸认证失败:', error)
    verificationResult.value = {
      success: false,
      title: '认证失败',
      message: '网络错误或服务暂时不可用，请稍后重试'
    }
    ElMessage.error('认证失败，请稍后重试')
  } finally {
    verifying.value = false
  }
}

// 重置认证
const resetVerification = () => {
  verificationStep.value = 0
  verificationResult.value = null
  capturedImage.value = ''
}

// 返回
const goBack = () => {
  // 根据具体需求决定返回逻辑
  window.history.back()
}

// 组件挂载时的处理
onMounted(() => {
  // 检查浏览器是否支持摄像头
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    ElMessage.error('您的浏览器不支持摄像头功能')
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  stopCamera()
})
</script>

<style scoped>
.face-verification-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.verification-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  text-align: center;
  margin-bottom: 30px;
}

.card-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.card-header p {
  color: #7f8c8d;
  font-size: 14px;
}

.camera-section {
  text-align: center;
}

.camera-container {
  position: relative;
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  background: #f5f5f5;
  margin-bottom: 20px;
}

video {
  width: 480px;
  height: 360px;
  object-fit: cover;
  background: #000;
}

.camera-active {
  border: 3px solid #67c23a;
}

.face-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.guide-circle {
  width: 200px;
  height: 200px;
  border: 3px dashed #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.face-detected .guide-circle {
  border-color: #67c23a;
  border-style: solid;
}

.guide-text {
  color: #409eff;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
}

.face-detected .guide-text {
  color: #67c23a;
}

.camera-controls {
  padding: 20px;
}

.control-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.photo-preview {
  margin-top: 30px;
}

.photo-preview h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.photo-preview img {
  max-width: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-actions {
  margin-top: 15px;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.verification-progress {
  margin: 30px 0;
}

.verification-result {
  margin-top: 30px;
}

.verification-tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.verification-tips h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.tip-icon {
  color: #409eff;
  font-size: 24px;
  margin-top: 2px;
}

.tip-item h4 {
  color: #2c3e50;
  margin: 0 0 5px 0;
  font-size: 14px;
}

.tip-item p {
  color: #7f8c8d;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}
</style>
