<template>
  <div class="dashboard">
    <!-- 顶部导航 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="/logo.png" alt="校园搭子" class="logo" />
          <h1>校园搭子系统</h1>
        </div>
        <div class="user-section">
          <el-dropdown @command="handleUserCommand">
            <span class="user-info">
              <el-avatar :size="40" :src="userInfo.avatar" />
              <span class="username">{{ userInfo.nickname }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-main">
      <!-- 侧边导航 -->
      <div class="sidebar">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          @select="handleMenuSelect"
        >
          <el-menu-item index="overview">
            <el-icon><Odometer /></el-icon>
            <span>总览</span>
          </el-menu-item>
          
          <el-sub-menu index="request">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>需求管理</span>
            </template>
            <el-menu-item index="publish">发布需求</el-menu-item>
            <el-menu-item index="square">需求广场</el-menu-item>
            <el-menu-item index="my-requests">我的需求</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="match">
            <template #title>
              <el-icon><Connection /></el-icon>
              <span>匹配系统</span>
            </template>
            <el-menu-item index="match-center">匹配中心</el-menu-item>
            <el-menu-item index="match-history">匹配记录</el-menu-item>
            <el-menu-item index="match-settings">匹配设置</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="verification">
            <template #title>
              <el-icon><UserFilled /></el-icon>
              <span>身份认证</span>
            </template>
            <el-menu-item index="student-verification">学号认证</el-menu-item>
            <el-menu-item index="face-verification">人脸认证</el-menu-item>
            <el-menu-item index="verification-status">认证状态</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="evaluation">
            <template #title>
              <el-icon><Star /></el-icon>
              <span>评价系统</span>
            </template>
            <el-menu-item index="pending-evaluations">待评价</el-menu-item>
            <el-menu-item index="my-evaluations">我的评价</el-menu-item>
            <el-menu-item index="received-evaluations">收到的评价</el-menu-item>
          </el-sub-menu>

          <el-menu-item index="messages">
            <el-icon><ChatDotRound /></el-icon>
            <span>消息中心</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 总览页面 -->
        <div v-if="activeMenu === 'overview'" class="overview-content">
          <div class="welcome-section">
            <h2>欢迎回来，{{ userInfo.nickname }}！</h2>
            <p>今天是 {{ currentDate }}，让我们开始新的校园搭子之旅吧</p>
          </div>

          <!-- 统计卡片 -->
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon requests">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.totalRequests }}</h3>
                <p>发布的需求</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon matches">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.totalMatches }}</h3>
                <p>成功匹配</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon evaluations">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.averageRating }}</h3>
                <p>平均评分</p>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon activities">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-info">
                <h3>{{ stats.completedActivities }}</h3>
                <p>完成活动</p>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="quick-actions">
            <h3>快速操作</h3>
            <div class="actions-grid">
              <el-button
                type="primary"
                size="large"
                @click="navigateTo('publish')"
              >
                <el-icon><Plus /></el-icon>
                发布新需求
              </el-button>
              
              <el-button
                size="large"
                @click="navigateTo('match-center')"
              >
                <el-icon><Search /></el-icon>
                开始匹配
              </el-button>
              
              <el-button
                size="large"
                @click="navigateTo('square')"
              >
                <el-icon><Grid /></el-icon>
                浏览需求
              </el-button>
              
              <el-button
                size="large"
                @click="navigateTo('pending-evaluations')"
              >
                <el-icon><EditPen /></el-icon>
                评价搭子
              </el-button>
            </div>
          </div>

          <!-- 最近活动 -->
          <div class="recent-activities">
            <h3>最近活动</h3>
            <el-timeline>
              <el-timeline-item
                v-for="activity in recentActivities"
                :key="activity.id"
                :timestamp="activity.time"
                :type="activity.type"
              >
                {{ activity.description }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>

        <!-- 其他页面内容通过路由组件显示 -->
        <router-view v-else />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="dashboard-footer">
      <div class="footer-content">
        <div class="status-info">
          <span class="online-status">
            <el-icon class="status-dot online"><CircleFilled /></el-icon>
            在线
          </span>
          <span class="last-sync">
            最后同步: {{ lastSyncTime }}
          </span>
        </div>
        <div class="system-info">
          <span>校园搭子系统 v1.0.0</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  ArrowDown,
  Odometer,
  Document,
  Connection,
  UserFilled,
  Star,
  ChatDotRound,
  Plus,
  Search,
  Grid,
  EditPen,
  Trophy,
  CircleFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const activeMenu = ref('overview')
const userInfo = reactive({
  nickname: '张同学',
  avatar: '/avatars/default.jpg'
})

const stats = reactive({
  totalRequests: 12,
  totalMatches: 8,
  averageRating: 4.6,
  completedActivities: 15
})

const recentActivities = ref([
  {
    id: 1,
    description: '与李同学完成了图书馆学习活动',
    time: '2024-01-15 16:30',
    type: 'success'
  },
  {
    id: 2,
    description: '发布了新的运动搭子需求',
    time: '2024-01-15 14:20',
    type: 'primary'
  },
  {
    id: 3,
    description: '收到了来自王同学的好评',
    time: '2024-01-15 12:10',
    type: 'success'
  }
])

// 计算属性
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

const lastSyncTime = computed(() => {
  return new Date().toLocaleTimeString('zh-CN')
})

// 方法
const handleMenuSelect = (index) => {
  activeMenu.value = index
  navigateTo(index)
}

const navigateTo = (route) => {
  const routeMap = {
    'overview': '/dashboard',
    'publish': '/publish',
    'square': '/square',
    'my-requests': '/my-requests',
    'match-center': '/match-center',
    'match-history': '/match-history',
    'match-settings': '/match-settings',
    'student-verification': '/verification',
    'face-verification': '/verification/face',
    'verification-status': '/verification/status',
    'pending-evaluations': '/evaluation/pending',
    'my-evaluations': '/evaluation/my',
    'received-evaluations': '/evaluation/received',
    'messages': '/messages'
  }
  
  const targetRoute = routeMap[route]
  if (targetRoute) {
    router.push(targetRoute)
  }
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  } catch (error) {
    ElMessage.error('退出登录失败')
  }
}

// 初始化
onMounted(() => {
  // 加载用户信息和统计数据
  loadUserData()
})

const loadUserData = async () => {
  try {
    // 这里应该调用API获取用户数据
    // const userData = await getUserInfo()
    // Object.assign(userInfo, userData)
  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}
</script>

<style scoped>
.dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
}

.header-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 40px;
  height: 40px;
}

.logo-section h1 {
  color: #2c3e50;
  font-size: 20px;
  margin: 0;
}

.user-section .user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-section .user-info:hover {
  background: #f5f5f5;
}

.username {
  color: #2c3e50;
  font-weight: 500;
}

.dashboard-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid #e4e7ed;
}

.sidebar-menu {
  border: none;
  height: 100%;
}

.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.overview-content {
  max-width: 1200px;
}

.welcome-section {
  background: white;
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-section h2 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.welcome-section p {
  color: #7f8c8d;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.requests { background: #3498db; }
.stat-icon.matches { background: #27ae60; }
.stat-icon.evaluations { background: #f39c12; }
.stat-icon.activities { background: #9b59b6; }

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #2c3e50;
}

.stat-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.quick-actions {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.actions-grid {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.recent-activities {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recent-activities h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.dashboard-footer {
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 10px 20px;
  height: 40px;
  display: flex;
  align-items: center;
}

.footer-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #7f8c8d;
}

.status-info {
  display: flex;
  gap: 20px;
}

.online-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-dot.online {
  color: #27ae60;
  font-size: 8px;
}
</style>
