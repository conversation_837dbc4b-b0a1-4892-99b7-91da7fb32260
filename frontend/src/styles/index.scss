// 导入变量
@import './variables.scss';

// 全局样式重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 通用工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.cursor-pointer {
  cursor: pointer;
}

// 间距工具类
@for $i from 0 through 50 {
  .m-#{$i} { margin: #{$i}px; }
  .mt-#{$i} { margin-top: #{$i}px; }
  .mr-#{$i} { margin-right: #{$i}px; }
  .mb-#{$i} { margin-bottom: #{$i}px; }
  .ml-#{$i} { margin-left: #{$i}px; }
  .mx-#{$i} { margin-left: #{$i}px; margin-right: #{$i}px; }
  .my-#{$i} { margin-top: #{$i}px; margin-bottom: #{$i}px; }
  
  .p-#{$i} { padding: #{$i}px; }
  .pt-#{$i} { padding-top: #{$i}px; }
  .pr-#{$i} { padding-right: #{$i}px; }
  .pb-#{$i} { padding-bottom: #{$i}px; }
  .pl-#{$i} { padding-left: #{$i}px; }
  .px-#{$i} { padding-left: #{$i}px; padding-right: #{$i}px; }
  .py-#{$i} { padding-top: #{$i}px; padding-bottom: #{$i}px; }
}

// 页面容器
.page-container {
  min-height: calc(100vh - 60px);
  padding: 20px;
  background-color: var(--el-bg-color-page);
}

.card-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
  margin-bottom: 20px;
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

// 响应式
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .card-container {
    padding: 15px;
    margin-bottom: 15px;
  }
}

// Element Plus 样式覆盖
.el-button {
  border-radius: 6px;
}

.el-card {
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
}

.el-input__wrapper {
  border-radius: 6px;
}

.el-select .el-input__wrapper {
  border-radius: 6px;
}

// 自定义组件样式
.buddy-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--el-box-shadow);
  }
}

.match-status {
  &.waiting {
    color: var(--el-color-warning);
  }
  
  &.matched {
    color: var(--el-color-success);
  }
  
  &.expired {
    color: var(--el-color-info);
  }
}
