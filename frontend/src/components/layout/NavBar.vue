<template>
  <div class="navbar">
    <el-container>
      <el-header class="navbar-header">
        <!-- Logo和标题 -->
        <div class="navbar-brand" @click="goHome">
          <el-icon class="brand-icon"><Connection /></el-icon>
          <span class="brand-text">校园搭子</span>
        </div>

        <!-- 导航菜单 -->
        <el-menu
          :default-active="activeIndex"
          mode="horizontal"
          class="navbar-menu"
          @select="handleMenuSelect"
        >
          <el-menu-item index="/home">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-menu-item index="/square">
            <el-icon><Grid /></el-icon>
            <span>需求广场</span>
          </el-menu-item>
          
          <el-menu-item index="/publish">
            <el-icon><EditPen /></el-icon>
            <span>发布需求</span>
          </el-menu-item>
          
          <el-menu-item index="/match">
            <el-icon><Magnet /></el-icon>
            <span>匹配中心</span>
          </el-menu-item>
          
          <el-menu-item index="/messages">
            <el-icon><ChatDotRound /></el-icon>
            <span>消息</span>
            <el-badge
              v-if="unreadCount > 0"
              :value="unreadCount"
              :max="99"
              class="message-badge"
            />
          </el-menu-item>
        </el-menu>

        <!-- 用户信息和操作 -->
        <div class="navbar-user">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索需求..."
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 通知 -->
          <el-dropdown @command="handleNotificationCommand" class="notification-dropdown">
            <el-button circle class="notification-btn">
              <el-icon><Bell /></el-icon>
              <el-badge
                v-if="notificationCount > 0"
                :value="notificationCount"
                :max="99"
                class="notification-badge"
              />
            </el-button>
            
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="view-all">
                  <el-icon><View /></el-icon>
                  查看所有通知
                </el-dropdown-item>
                <el-dropdown-item command="mark-read">
                  <el-icon><Check /></el-icon>
                  标记全部已读
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  通知设置
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand" class="user-dropdown">
            <div class="user-info">
              <el-avatar :src="userStore.user?.avatar" :size="36">
                {{ getAvatarText() }}
              </el-avatar>
              <span class="username">{{ userStore.user?.nickname || '用户' }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="my-requests">
                  <el-icon><Document /></el-icon>
                  我的需求
                </el-dropdown-item>
                <el-dropdown-item command="favorites">
                  <el-icon><Star /></el-icon>
                  我的收藏
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Connection,
  House,
  Grid,
  EditPen,
  Magnet,
  ChatDotRound,
  Search,
  Bell,
  ArrowDown,
  User,
  Document,
  Star,
  Setting,
  SwitchButton,
  View,
  Check
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const searchKeyword = ref('')
const unreadCount = ref(0)
const notificationCount = ref(0)

// 计算属性
const activeIndex = computed(() => {
  return route.path
})

// 方法
const goHome = () => {
  router.push('/home')
}

const handleMenuSelect = (index) => {
  router.push(index)
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/square',
      query: { keyword: searchKeyword.value.trim() }
    })
    searchKeyword.value = ''
  }
}

const handleNotificationCommand = (command) => {
  switch (command) {
    case 'view-all':
      router.push('/notifications')
      break
    case 'mark-read':
      markAllNotificationsRead()
      break
    case 'settings':
      router.push('/settings/notifications')
      break
  }
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'my-requests':
      router.push('/profile/requests')
      break
    case 'favorites':
      router.push('/profile/favorites')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    // 用户取消
  }
}

const markAllNotificationsRead = () => {
  // 标记所有通知为已读
  notificationCount.value = 0
  ElMessage.success('所有通知已标记为已读')
}

const getAvatarText = () => {
  const nickname = userStore.user?.nickname
  return nickname ? nickname.charAt(0).toUpperCase() : 'U'
}

const fetchUnreadCounts = async () => {
  // 模拟获取未读消息和通知数量
  try {
    // 这里应该调用实际的API
    unreadCount.value = 3
    notificationCount.value = 5
  } catch (error) {
    console.error('获取未读数量失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchUnreadCounts()
})
</script>

<style lang="scss" scoped>
.navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: var(--el-box-shadow-light);
  
  .navbar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
  }
  
  .navbar-brand {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      color: var(--el-color-primary);
    }
    
    .brand-icon {
      font-size: 28px;
      color: var(--el-color-primary);
      margin-right: 8px;
    }
    
    .brand-text {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  .navbar-menu {
    flex: 1;
    margin: 0 40px;
    border-bottom: none;
    
    .el-menu-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: 500;
      
      .el-icon {
        font-size: 18px;
      }
      
      .message-badge {
        margin-left: 4px;
      }
    }
  }
  
  .navbar-user {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .search-box {
      .search-input {
        width: 250px;
      }
    }
    
    .notification-dropdown {
      .notification-btn {
        position: relative;
        
        .notification-badge {
          position: absolute;
          top: -8px;
          right: -8px;
        }
      }
    }
    
    .user-dropdown {
      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 6px;
        transition: all 0.3s ease;
        
        &:hover {
          background: var(--el-fill-color-light);
        }
        
        .username {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
        
        .dropdown-icon {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

@media (max-width: 1024px) {
  .navbar {
    .navbar-menu {
      margin: 0 20px;
    }
    
    .search-box {
      .search-input {
        width: 200px;
      }
    }
  }
}

@media (max-width: 768px) {
  .navbar {
    .navbar-header {
      padding: 0 12px;
    }
    
    .navbar-menu {
      display: none;
    }
    
    .search-box {
      .search-input {
        width: 150px;
      }
    }
    
    .user-info {
      .username {
        display: none;
      }
    }
  }
}
</style>
