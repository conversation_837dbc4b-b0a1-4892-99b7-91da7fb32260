<template>
  <el-dialog
    v-model="visible"
    title="申请匹配"
    width="500px"
    :before-close="handleClose"
    class="apply-match-dialog"
  >
    <div class="dialog-content">
      <!-- 需求信息 -->
      <div class="request-summary">
        <h4>申请需求</h4>
        <div class="summary-card">
          <div class="summary-header">
            <el-tag :type="getRequestTypeColor(request.type)" size="small">
              {{ request.type?.name }}
            </el-tag>
            <span class="request-title">{{ request.title }}</span>
          </div>
          
          <div class="summary-info">
            <div class="info-item">
              <el-icon><Clock /></el-icon>
              <span>{{ formatTimeRange(request.startTime, request.endTime) }}</span>
            </div>
            <div class="info-item">
              <el-icon><Location /></el-icon>
              <span>{{ request.location?.name }}</span>
            </div>
            <div class="info-item">
              <el-icon><User /></el-icon>
              <span>{{ request.currentParticipants }}/{{ request.maxParticipants }}人</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 申请表单 -->
      <div class="apply-form">
        <h4>申请信息</h4>
        
        <!-- 自我介绍 -->
        <el-form-item label="自我介绍">
          <el-input
            v-model="form.introduction"
            type="textarea"
            :rows="3"
            placeholder="简单介绍一下自己，让对方更了解你"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 申请理由 -->
        <el-form-item label="申请理由">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="3"
            placeholder="说明为什么想要参与这个需求"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 相关经验 -->
        <el-form-item label="相关经验">
          <el-input
            v-model="form.experience"
            type="textarea"
            :rows="2"
            placeholder="分享相关的经验或技能（可选）"
            maxlength="150"
            show-word-limit
          />
        </el-form-item>

        <!-- 联系方式确认 -->
        <el-form-item label="联系方式">
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span>微信：{{ userStore.user?.wechat || '未设置' }}</span>
              <el-button
                v-if="!userStore.user?.wechat"
                type="text"
                size="small"
                @click="goToProfile"
              >
                去设置
              </el-button>
            </div>
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>手机：{{ formatPhone(userStore.user?.phone) || '未设置' }}</span>
              <el-button
                v-if="!userStore.user?.phone"
                type="text"
                size="small"
                @click="goToProfile"
              >
                去设置
              </el-button>
            </div>
          </div>
          <div class="contact-tip">
            <el-text size="small" type="info">
              <el-icon><InfoFilled /></el-icon>
              申请成功后，对方可以看到你的联系方式
            </el-text>
          </div>
        </el-form-item>

        <!-- 时间确认 -->
        <el-form-item label="时间确认">
          <el-checkbox v-model="form.timeConfirmed">
            我确认可以在指定时间参与此需求
          </el-checkbox>
        </el-form-item>

        <!-- 规则同意 -->
        <el-form-item>
          <el-checkbox v-model="form.rulesAgreed">
            我已阅读并同意
            <el-button type="text" @click="showRules = true">《校园搭子匹配规则》</el-button>
          </el-checkbox>
        </el-form-item>
      </div>

      <!-- 快速模板 -->
      <div class="quick-templates">
        <h4>快速模板</h4>
        <div class="template-list">
          <el-button
            v-for="template in templates"
            :key="template.id"
            size="small"
            type="primary"
            plain
            @click="useTemplate(template)"
            class="template-btn"
          >
            {{ template.name }}
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!canSubmit"
          :loading="loading"
        >
          发送申请
        </el-button>
      </div>
    </template>

    <!-- 规则对话框 -->
    <el-dialog
      v-model="showRules"
      title="校园搭子匹配规则"
      width="600px"
      class="rules-dialog"
    >
      <div class="rules-content">
        <h4>基本规则</h4>
        <ul>
          <li>保持诚信，按时参与约定的活动</li>
          <li>尊重他人，友善交流</li>
          <li>保护个人隐私，不泄露他人信息</li>
          <li>如有变动，及时沟通</li>
        </ul>

        <h4>安全提醒</h4>
        <ul>
          <li>首次见面建议选择公共场所</li>
          <li>保护个人财物和信息安全</li>
          <li>如遇不当行为，及时举报</li>
        </ul>

        <h4>违规处理</h4>
        <ul>
          <li>恶意爽约将影响信用评分</li>
          <li>不当行为将被限制使用</li>
          <li>严重违规将被永久封禁</li>
        </ul>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="showRules = false">我知道了</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Clock,
  Location,
  User,
  Message,
  Phone,
  InfoFilled
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  request: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const visible = ref(props.modelValue)
const loading = ref(false)
const showRules = ref(false)

const form = ref({
  introduction: '',
  reason: '',
  experience: '',
  timeConfirmed: false,
  rulesAgreed: false
})

// 快速模板
const templates = [
  {
    id: 1,
    name: '学习型',
    introduction: '我是一个认真学习的学生，喜欢和志同道合的同学一起进步。',
    reason: '希望能找到学习伙伴，互相督促，共同提高。'
  },
  {
    id: 2,
    name: '运动型',
    introduction: '我热爱运动，有一定的运动基础，性格开朗。',
    reason: '想要找到运动伙伴，一起锻炼身体，享受运动的快乐。'
  },
  {
    id: 3,
    name: '社交型',
    introduction: '我性格外向，喜欢交朋友，善于沟通。',
    reason: '希望能认识新朋友，一起参与有趣的活动。'
  },
  {
    id: 4,
    name: '简洁型',
    introduction: '我是一个靠谱的人，会按时参与约定的活动。',
    reason: '对这个需求很感兴趣，希望能够参与。'
  }
]

// 计算属性
const canSubmit = computed(() => {
  return form.value.introduction.trim() &&
         form.value.reason.trim() &&
         form.value.timeConfirmed &&
         form.value.rulesAgreed &&
         (userStore.user?.wechat || userStore.user?.phone)
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const getRequestTypeColor = (type) => {
  const colorMap = {
    '学习搭子': 'primary',
    '运动搭子': 'success',
    '饭友': 'warning',
    '图书馆搭子': 'info'
  }
  return colorMap[type?.name] || 'primary'
}

const formatTimeRange = (startTime, endTime) => {
  if (!startTime) return ''
  
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : null
  
  const formatDateTime = (date) => {
    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
  }
  
  if (end) {
    return `${formatDateTime(start)} - ${formatDateTime(end)}`
  }
  
  return formatDateTime(start)
}

const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

const useTemplate = (template) => {
  form.value.introduction = template.introduction
  form.value.reason = template.reason
  ElMessage.success('模板已应用')
}

const goToProfile = () => {
  router.push('/profile')
}

const handleClose = () => {
  visible.value = false
  // 重置表单
  form.value = {
    introduction: '',
    reason: '',
    experience: '',
    timeConfirmed: false,
    rulesAgreed: false
  }
}

const handleConfirm = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('请完善申请信息')
    return
  }
  
  loading.value = true
  
  try {
    const applyData = {
      introduction: form.value.introduction.trim(),
      reason: form.value.reason.trim(),
      experience: form.value.experience.trim(),
      timeConfirmed: form.value.timeConfirmed,
      rulesAgreed: form.value.rulesAgreed
    }
    
    emit('confirm', applyData)
  } catch (error) {
    console.error('申请失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.apply-match-dialog {
  .dialog-content {
    max-height: 70vh;
    overflow-y: auto;
    
    h4 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .request-summary {
    margin-bottom: 24px;
    
    .summary-card {
      padding: 16px;
      background: var(--el-fill-color-lighter);
      border-radius: 8px;
      
      .summary-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        
        .request-title {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }
      
      .summary-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .info-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: var(--el-text-color-regular);
          
          .el-icon {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
  
  .apply-form {
    margin-bottom: 24px;
    
    .contact-info {
      margin-bottom: 8px;
      
      .contact-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 14px;
        
        .el-icon {
          color: var(--el-color-primary);
        }
      }
    }
    
    .contact-tip {
      padding: 8px 12px;
      background: var(--el-color-info-light-9);
      border-radius: 4px;
      
      .el-text {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .quick-templates {
    .template-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .template-btn {
        font-size: 12px;
      }
    }
  }
}

.rules-dialog {
  .rules-content {
    h4 {
      margin: 16px 0 8px 0;
      color: var(--el-text-color-primary);
      font-size: 16px;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    ul {
      margin: 0 0 16px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.6;
        color: var(--el-text-color-regular);
      }
    }
  }
}

@media (max-width: 768px) {
  .apply-match-dialog {
    .summary-info {
      .info-item {
        font-size: 12px;
      }
    }
    
    .template-list {
      justify-content: center;
    }
  }
}
</style>
