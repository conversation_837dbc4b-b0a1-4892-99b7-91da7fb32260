<template>
  <div class="location-selector">
    <div class="location-input">
      <el-input
        v-model="locationText"
        placeholder="输入或选择位置"
        readonly
        @click="showLocationDialog = true"
      >
        <template #prefix>
          <el-icon><Location /></el-icon>
        </template>
        <template #suffix>
          <el-button
            v-if="locationText"
            type="text"
            size="small"
            @click.stop="clearLocation"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 位置选择对话框 -->
    <el-dialog
      v-model="showLocationDialog"
      title="选择位置"
      width="600px"
      :before-close="handleDialogClose"
    >
      <div class="location-dialog">
        <!-- 搜索框 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索位置"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 常用位置 -->
        <div class="common-locations">
          <h4>常用位置</h4>
          <div class="location-grid">
            <div
              v-for="location in commonLocations"
              :key="location.id"
              class="location-item"
              :class="{ active: selectedLocation?.id === location.id }"
              @click="selectLocation(location)"
            >
              <el-icon class="location-icon">
                <component :is="location.icon" />
              </el-icon>
              <div class="location-info">
                <div class="location-name">{{ location.name }}</div>
                <div class="location-desc">{{ location.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 校园建筑 -->
        <div class="campus-buildings">
          <h4>校园建筑</h4>
          <el-tabs v-model="activeTab" class="building-tabs">
            <el-tab-pane
              v-for="category in buildingCategories"
              :key="category.key"
              :label="category.label"
              :name="category.key"
            >
              <div class="building-grid">
                <div
                  v-for="building in getBuildingsByCategory(category.key)"
                  :key="building.id"
                  class="location-item"
                  :class="{ active: selectedLocation?.id === building.id }"
                  @click="selectLocation(building)"
                >
                  <el-icon class="location-icon">
                    <component :is="building.icon" />
                  </el-icon>
                  <div class="location-info">
                    <div class="location-name">{{ building.name }}</div>
                    <div class="location-desc">{{ building.description }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 自定义位置 -->
        <div class="custom-location">
          <h4>自定义位置</h4>
          <div class="custom-input-group">
            <el-input
              v-model="customLocationName"
              placeholder="输入位置名称"
              class="custom-input"
            />
            <el-input
              v-model="customLocationDesc"
              placeholder="输入位置描述（可选）"
              class="custom-input"
            />
            <el-button @click="addCustomLocation" type="primary">
              添加位置
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showLocationDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmLocation"
            :disabled="!selectedLocation"
          >
            确认选择
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Location,
  Close,
  Search,
  School,
  Reading,
  Food,
  Basketball,
  Coffee,
  Shop,
  Car,
  House
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const showLocationDialog = ref(false)
const selectedLocation = ref(props.modelValue)
const searchKeyword = ref('')
const activeTab = ref('academic')
const customLocationName = ref('')
const customLocationDesc = ref('')

// 常用位置
const commonLocations = [
  {
    id: 'library_main',
    name: '图书馆',
    description: '主图书馆',
    icon: 'Reading',
    category: 'academic'
  },
  {
    id: 'canteen_1',
    name: '第一食堂',
    description: '一楼大厅',
    icon: 'Food',
    category: 'dining'
  },
  {
    id: 'gym_main',
    name: '体育馆',
    description: '主体育馆',
    icon: 'Basketball',
    category: 'sports'
  },
  {
    id: 'cafe_central',
    name: '中心咖啡厅',
    description: '学生活动中心',
    icon: 'Coffee',
    category: 'leisure'
  }
]

// 建筑分类
const buildingCategories = [
  { key: 'academic', label: '教学楼' },
  { key: 'dining', label: '餐饮' },
  { key: 'sports', label: '运动场所' },
  { key: 'leisure', label: '休闲娱乐' },
  { key: 'dormitory', label: '宿舍区' },
  { key: 'other', label: '其他' }
]

// 校园建筑数据
const campusBuildings = [
  // 教学楼
  {
    id: 'teaching_1',
    name: '第一教学楼',
    description: '1-6楼教室',
    icon: 'School',
    category: 'academic'
  },
  {
    id: 'teaching_2',
    name: '第二教学楼',
    description: '1-8楼教室',
    icon: 'School',
    category: 'academic'
  },
  {
    id: 'library_1',
    name: '图书馆一楼',
    description: '阅览室',
    icon: 'Reading',
    category: 'academic'
  },
  {
    id: 'library_2',
    name: '图书馆二楼',
    description: '自习室',
    icon: 'Reading',
    category: 'academic'
  },
  {
    id: 'library_3',
    name: '图书馆三楼',
    description: '安静自习区',
    icon: 'Reading',
    category: 'academic'
  },
  
  // 餐饮
  {
    id: 'canteen_1_1f',
    name: '第一食堂一楼',
    description: '大众餐厅',
    icon: 'Food',
    category: 'dining'
  },
  {
    id: 'canteen_1_2f',
    name: '第一食堂二楼',
    description: '自选餐区',
    icon: 'Food',
    category: 'dining'
  },
  {
    id: 'canteen_2',
    name: '第二食堂',
    description: '特色餐厅',
    icon: 'Food',
    category: 'dining'
  },
  {
    id: 'cafe_1',
    name: '星巴克',
    description: '图书馆一楼',
    icon: 'Coffee',
    category: 'dining'
  },
  
  // 运动场所
  {
    id: 'gym_basketball',
    name: '篮球场',
    description: '室外篮球场',
    icon: 'Basketball',
    category: 'sports'
  },
  {
    id: 'gym_indoor',
    name: '室内体育馆',
    description: '羽毛球、乒乓球',
    icon: 'Basketball',
    category: 'sports'
  },
  {
    id: 'playground',
    name: '操场',
    description: '田径场',
    icon: 'Basketball',
    category: 'sports'
  },
  
  // 休闲娱乐
  {
    id: 'student_center',
    name: '学生活动中心',
    description: '多功能厅',
    icon: 'House',
    category: 'leisure'
  },
  {
    id: 'park_central',
    name: '中心花园',
    description: '休闲区域',
    icon: 'House',
    category: 'leisure'
  },
  
  // 其他
  {
    id: 'parking_1',
    name: '停车场A区',
    description: '主入口停车场',
    icon: 'Car',
    category: 'other'
  },
  {
    id: 'supermarket',
    name: '校园超市',
    description: '生活用品',
    icon: 'Shop',
    category: 'other'
  }
]

// 计算属性
const locationText = computed(() => {
  if (!selectedLocation.value) return ''
  return `${selectedLocation.value.name} - ${selectedLocation.value.description}`
})

const filteredBuildings = computed(() => {
  if (!searchKeyword.value) return campusBuildings
  
  const keyword = searchKeyword.value.toLowerCase()
  return campusBuildings.filter(building =>
    building.name.toLowerCase().includes(keyword) ||
    building.description.toLowerCase().includes(keyword)
  )
})

// 根据分类获取建筑
const getBuildingsByCategory = (category) => {
  return filteredBuildings.value.filter(building => building.category === category)
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedLocation.value = newVal
})

// 方法
const selectLocation = (location) => {
  selectedLocation.value = location
}

const confirmLocation = () => {
  emit('update:modelValue', selectedLocation.value)
  showLocationDialog.value = false
}

const clearLocation = () => {
  selectedLocation.value = null
  emit('update:modelValue', null)
}

const handleDialogClose = () => {
  // 恢复到原始选择
  selectedLocation.value = props.modelValue
  showLocationDialog.value = false
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const addCustomLocation = () => {
  if (!customLocationName.value.trim()) {
    ElMessage.warning('请输入位置名称')
    return
  }
  
  const customLocation = {
    id: `custom_${Date.now()}`,
    name: customLocationName.value.trim(),
    description: customLocationDesc.value.trim() || '自定义位置',
    icon: 'Location',
    category: 'other',
    custom: true
  }
  
  selectLocation(customLocation)
  customLocationName.value = ''
  customLocationDesc.value = ''
  ElMessage.success('自定义位置已添加')
}
</script>

<style lang="scss" scoped>
.location-selector {
  width: 100%;
}

.location-dialog {
  .search-section {
    margin-bottom: 20px;
  }
  
  .common-locations,
  .campus-buildings,
  .custom-location {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .location-grid,
  .building-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
    max-height: 200px;
    overflow-y: auto;
  }
  
  .location-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
    
    &.active {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-8);
    }
    
    .location-icon {
      font-size: 20px;
      color: var(--el-color-primary);
      margin-right: 12px;
    }
    
    .location-info {
      flex: 1;
      
      .location-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 2px;
      }
      
      .location-desc {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  .building-tabs {
    :deep(.el-tabs__content) {
      padding-top: 16px;
    }
  }
  
  .custom-input-group {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    
    .custom-input {
      flex: 1;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .location-grid,
  .building-grid {
    grid-template-columns: 1fr;
  }
  
  .custom-input-group {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
