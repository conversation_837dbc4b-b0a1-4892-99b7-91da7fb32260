<template>
  <div class="tag-input">
    <!-- 已选择的标签 -->
    <div class="selected-tags">
      <el-tag
        v-for="(tag, index) in selectedTags"
        :key="index"
        closable
        :type="getTagType(tag)"
        @close="removeTag(index)"
        class="tag-item"
      >
        {{ tag }}
      </el-tag>
      
      <!-- 输入框 -->
      <el-input
        v-if="showInput"
        ref="inputRef"
        v-model="inputValue"
        size="small"
        class="tag-input-field"
        @keyup.enter="addTag"
        @blur="addTag"
        @keyup.escape="cancelInput"
      />
      
      <!-- 添加按钮 -->
      <el-button
        v-else
        size="small"
        type="primary"
        plain
        @click="showInputField"
        class="add-tag-btn"
      >
        <el-icon><Plus /></el-icon>
        添加标签
      </el-button>
    </div>

    <!-- 推荐标签 -->
    <div v-if="recommendedTags.length > 0" class="recommended-tags">
      <div class="section-title">推荐标签</div>
      <div class="tag-list">
        <el-tag
          v-for="tag in availableRecommendedTags"
          :key="tag"
          class="recommended-tag"
          :type="getTagType(tag)"
          @click="selectRecommendedTag(tag)"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <!-- 热门标签 -->
    <div v-if="hotTags.length > 0" class="hot-tags">
      <div class="section-title">热门标签</div>
      <div class="tag-list">
        <el-tag
          v-for="tag in availableHotTags"
          :key="tag"
          class="hot-tag"
          :type="getTagType(tag)"
          @click="selectHotTag(tag)"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <!-- 标签提示 -->
    <div class="tag-tips">
      <el-text size="small" type="info">
        <el-icon><InfoFilled /></el-icon>
        最多可添加{{ maxTags }}个标签，每个标签不超过{{ maxTagLength }}个字符
      </el-text>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxTags: {
    type: Number,
    default: 8
  },
  maxTagLength: {
    type: Number,
    default: 10
  },
  placeholder: {
    type: String,
    default: '输入标签'
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const selectedTags = ref([...props.modelValue])
const showInput = ref(false)
const inputValue = ref('')
const inputRef = ref()

// 推荐标签（根据需求类型动态生成）
const recommendedTags = ref([
  '学霸', '认真', '准时', '安静', '友善',
  '有趣', '健谈', '专注', '互助', '长期'
])

// 热门标签
const hotTags = ref([
  '考研党', '四六级', '期末复习', '课业讨论',
  '运动达人', '美食爱好者', '摄影', '音乐',
  '社团活动', '志愿服务', '实习', '求职'
])

// 标签类型映射
const tagTypeMap = {
  '学霸': 'success',
  '认真': 'success',
  '准时': 'success',
  '安静': 'info',
  '友善': 'warning',
  '有趣': 'warning',
  '健谈': 'warning',
  '专注': 'info',
  '互助': 'success',
  '长期': 'info',
  '考研党': 'danger',
  '四六级': 'danger',
  '期末复习': 'danger',
  '课业讨论': 'primary',
  '运动达人': 'success',
  '美食爱好者': 'warning',
  '摄影': 'info',
  '音乐': 'info',
  '社团活动': 'primary',
  '志愿服务': 'success',
  '实习': 'danger',
  '求职': 'danger'
}

// 计算属性
const availableRecommendedTags = computed(() => {
  return recommendedTags.value.filter(tag => !selectedTags.value.includes(tag))
})

const availableHotTags = computed(() => {
  return hotTags.value.filter(tag => !selectedTags.value.includes(tag))
})

const canAddMoreTags = computed(() => {
  return selectedTags.value.length < props.maxTags
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedTags.value = [...newVal]
})

// 监听选中标签变化
watch(selectedTags, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 方法
const getTagType = (tag) => {
  return tagTypeMap[tag] || 'primary'
}

const showInputField = () => {
  if (!canAddMoreTags.value) {
    ElMessage.warning(`最多只能添加${props.maxTags}个标签`)
    return
  }
  
  showInput.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const addTag = () => {
  const tag = inputValue.value.trim()
  
  if (!tag) {
    hideInput()
    return
  }
  
  // 验证标签长度
  if (tag.length > props.maxTagLength) {
    ElMessage.warning(`标签长度不能超过${props.maxTagLength}个字符`)
    return
  }
  
  // 检查是否已存在
  if (selectedTags.value.includes(tag)) {
    ElMessage.warning('该标签已存在')
    inputValue.value = ''
    return
  }
  
  // 检查数量限制
  if (!canAddMoreTags.value) {
    ElMessage.warning(`最多只能添加${props.maxTags}个标签`)
    hideInput()
    return
  }
  
  // 添加标签
  selectedTags.value.push(tag)
  inputValue.value = ''
  
  // 如果达到最大数量，隐藏输入框
  if (!canAddMoreTags.value) {
    hideInput()
  } else {
    // 继续聚焦输入框
    nextTick(() => {
      inputRef.value?.focus()
    })
  }
}

const removeTag = (index) => {
  selectedTags.value.splice(index, 1)
}

const selectRecommendedTag = (tag) => {
  if (!canAddMoreTags.value) {
    ElMessage.warning(`最多只能添加${props.maxTags}个标签`)
    return
  }
  
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value.push(tag)
  }
}

const selectHotTag = (tag) => {
  if (!canAddMoreTags.value) {
    ElMessage.warning(`最多只能添加${props.maxTags}个标签`)
    return
  }
  
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value.push(tag)
  }
}

const cancelInput = () => {
  hideInput()
}

const hideInput = () => {
  showInput.value = false
  inputValue.value = ''
}
</script>

<style lang="scss" scoped>
.tag-input {
  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-bottom: 16px;
    min-height: 32px;
    
    .tag-item {
      cursor: default;
    }
    
    .tag-input-field {
      width: 120px;
    }
    
    .add-tag-btn {
      height: 24px;
      font-size: 12px;
      
      .el-icon {
        margin-right: 4px;
      }
    }
  }
  
  .recommended-tags,
  .hot-tags {
    margin-bottom: 16px;
    
    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }
    
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      
      .recommended-tag,
      .hot-tag {
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .tag-tips {
    padding: 8px 12px;
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
    
    .el-text {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

@media (max-width: 768px) {
  .tag-input {
    .selected-tags {
      justify-content: flex-start;
    }
    
    .tag-list {
      justify-content: flex-start;
    }
  }
}
</style>
