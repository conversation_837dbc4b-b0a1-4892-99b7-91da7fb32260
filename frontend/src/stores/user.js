import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(getToken())
  const userInfo = ref(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.username || '')
  const avatar = computed(() => userInfo.value?.avatar || '')
  const creditScore = computed(() => userInfo.value?.creditScore || 0)

  // 登录
  const login = async (loginData) => {
    loading.value = true
    try {
      const response = await userApi.login(loginData)
      if (response.success) {
        token.value = response.token
        setToken(response.token)
        await getUserInfo()
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '登录失败' }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData) => {
    loading.value = true
    try {
      const response = await userApi.register(registerData)
      if (response.success) {
        return { success: true, message: '注册成功' }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '注册失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    if (!token.value) return
    
    try {
      const response = await userApi.getUserInfo()
      if (response.success) {
        userInfo.value = response.data
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果token无效，清除登录状态
      if (error.response?.status === 401) {
        logout()
      }
    }
  }

  // 更新用户信息
  const updateUserInfo = async (userData) => {
    loading.value = true
    try {
      const response = await userApi.updateUser(userInfo.value.id, userData)
      if (response.success) {
        // 更新本地用户信息
        userInfo.value = { ...userInfo.value, ...userData }
        return { success: true, message: '更新成功' }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '更新失败' }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    userInfo.value = null
    removeToken()
  }

  // 初始化用户信息
  const initUser = async () => {
    if (token.value) {
      await getUserInfo()
    }
  }

  // 更新信用分数
  const updateCreditScore = (score) => {
    if (userInfo.value) {
      userInfo.value.creditScore = score
    }
  }

  return {
    // 状态
    token,
    userInfo,
    loading,
    
    // 计算属性
    isAuthenticated,
    userName,
    avatar,
    creditScore,
    
    // 方法
    login,
    register,
    getUserInfo,
    updateUserInfo,
    logout,
    initUser,
    updateCreditScore
  }
})
