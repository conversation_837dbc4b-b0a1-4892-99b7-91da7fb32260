import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { buddyRequestApi } from '@/api/buddy-request'
import { ElMessage } from 'element-plus'

export const useBuddyRequestStore = defineStore('buddyRequest', () => {
  // 状态
  const requests = ref([])
  const requestTypes = ref([])
  const conditionTemplates = ref([])
  const currentRequest = ref(null)
  const loading = ref(false)
  const total = ref(0)
  const filters = ref({
    typeId: null,
    keyword: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
    status: 0, // 0-待匹配
    page: 1,
    size: 20
  })

  // 我的需求
  const myRequests = ref([])
  const favoriteRequests = ref([])

  // 计算属性
  const activeRequests = computed(() => 
    requests.value.filter(req => req.status === 0)
  )

  const expiredRequests = computed(() => 
    requests.value.filter(req => req.status === 4)
  )

  const completedRequests = computed(() => 
    requests.value.filter(req => req.status === 2)
  )

  // 获取需求类型列表
  const fetchRequestTypes = async () => {
    try {
      const response = await buddyRequestApi.getRequestTypes()
      if (response.success) {
        requestTypes.value = response.data
      }
    } catch (error) {
      console.error('获取需求类型失败:', error)
    }
  }

  // 获取附加条件模板
  const fetchConditionTemplates = async () => {
    try {
      const response = await buddyRequestApi.getConditionTemplates()
      if (response.success) {
        conditionTemplates.value = response.data
      }
    } catch (error) {
      console.error('获取条件模板失败:', error)
    }
  }

  // 获取需求列表
  const fetchRequests = async (params = {}) => {
    loading.value = true
    try {
      const queryParams = { ...filters.value, ...params }
      const response = await buddyRequestApi.getRequests(queryParams)
      if (response.success) {
        if (params.page === 1) {
          requests.value = response.data.list
        } else {
          requests.value.push(...response.data.list)
        }
        total.value = response.data.total
        filters.value = { ...filters.value, ...params }
      }
    } catch (error) {
      console.error('获取需求列表失败:', error)
      ElMessage.error('获取需求列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取需求详情
  const fetchRequestDetail = async (id) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.getRequestDetail(id)
      if (response.success) {
        currentRequest.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取需求详情失败:', error)
      ElMessage.error('获取需求详情失败')
    } finally {
      loading.value = false
    }
  }

  // 更新需求
  const updateRequest = async (id, data) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.updateRequest(id, data)
      if (response.success) {
        // 更新当前需求
        if (currentRequest.value && currentRequest.value.id === id) {
          currentRequest.value = { ...currentRequest.value, ...response.data }
        }

        // 更新列表中的需求
        const index = requests.value.findIndex(req => req.id === id)
        if (index !== -1) {
          requests.value[index] = { ...requests.value[index], ...response.data }
        }

        ElMessage.success('需求更新成功')
      }
      return response
    } catch (error) {
      console.error('更新需求失败:', error)
      ElMessage.error('更新需求失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 搜索需求
  const searchRequests = async (keyword, params = {}) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.searchRequests({
        keyword,
        ...params
      })
      if (response.success) {
        if (params.page === 1) {
          requests.value = response.data.list
        } else {
          requests.value.push(...response.data.list)
        }
        total.value = response.data.total
      }
      return response
    } catch (error) {
      console.error('搜索需求失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取热门需求
  const fetchHotRequests = async (params = {}) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.getHotRequests(params)
      if (response.success) {
        if (params.page === 1) {
          requests.value = response.data.list
        } else {
          requests.value.push(...response.data.list)
        }
        total.value = response.data.total
      }
      return response
    } catch (error) {
      console.error('获取热门需求失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取推荐需求
  const fetchRecommendedRequests = async (params = {}) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.getRecommendedRequests(params)
      if (response.success) {
        if (params.page === 1) {
          requests.value = response.data.list
        } else {
          requests.value.push(...response.data.list)
        }
        total.value = response.data.total
      }
      return response
    } catch (error) {
      console.error('获取推荐需求失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取条件模板
  const fetchConditionTemplates = async () => {
    try {
      const response = await buddyRequestApi.getConditionTemplates()
      if (response.success) {
        conditionTemplates.value = response.data
      }
      return response
    } catch (error) {
      console.error('获取条件模板失败:', error)
      throw error
    }
  }

  // 发布需求
  const createRequest = async (requestData) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.createRequest(requestData)
      if (response.success) {
        ElMessage.success('需求发布成功')
        // 添加到列表开头
        requests.value.unshift(response.data)
        return { success: true, data: response.data }
      } else {
        ElMessage.error(response.message || '发布失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('发布需求失败:', error)
      ElMessage.error('发布需求失败')
      return { success: false, message: error.message }
    } finally {
      loading.value = false
    }
  }

  // 更新需求
  const updateRequest = async (id, requestData) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.updateRequest(id, requestData)
      if (response.success) {
        ElMessage.success('需求更新成功')
        // 更新列表中的数据
        const index = requests.value.findIndex(req => req.id === id)
        if (index !== -1) {
          requests.value[index] = { ...requests.value[index], ...requestData }
        }
        // 更新当前需求
        if (currentRequest.value && currentRequest.value.id === id) {
          currentRequest.value = { ...currentRequest.value, ...requestData }
        }
        return { success: true }
      } else {
        ElMessage.error(response.message || '更新失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('更新需求失败:', error)
      ElMessage.error('更新需求失败')
      return { success: false, message: error.message }
    } finally {
      loading.value = false
    }
  }

  // 取消需求
  const cancelRequest = async (id) => {
    try {
      const response = await buddyRequestApi.cancelRequest(id)
      if (response.success) {
        ElMessage.success('需求已取消')
        // 更新状态
        const index = requests.value.findIndex(req => req.id === id)
        if (index !== -1) {
          requests.value[index].status = 3 // 已取消
        }
        return { success: true }
      } else {
        ElMessage.error(response.message || '取消失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('取消需求失败:', error)
      ElMessage.error('取消需求失败')
      return { success: false, message: error.message }
    }
  }

  // 点赞需求
  const likeRequest = async (id) => {
    try {
      const response = await buddyRequestApi.likeRequest(id)
      if (response.success) {
        // 更新点赞状态
        const index = requests.value.findIndex(req => req.id === id)
        if (index !== -1) {
          requests.value[index].isLiked = true
          requests.value[index].likeCount = (requests.value[index].likeCount || 0) + 1
        }
        return { success: true }
      }
    } catch (error) {
      console.error('点赞失败:', error)
      ElMessage.error('点赞失败')
    }
  }

  // 取消点赞
  const unlikeRequest = async (id) => {
    try {
      const response = await buddyRequestApi.unlikeRequest(id)
      if (response.success) {
        // 更新点赞状态
        const index = requests.value.findIndex(req => req.id === id)
        if (index !== -1) {
          requests.value[index].isLiked = false
          requests.value[index].likeCount = Math.max((requests.value[index].likeCount || 0) - 1, 0)
        }
        return { success: true }
      }
    } catch (error) {
      console.error('取消点赞失败:', error)
      ElMessage.error('取消点赞失败')
    }
  }

  // 收藏需求
  const favoriteRequest = async (id) => {
    try {
      const response = await buddyRequestApi.favoriteRequest(id)
      if (response.success) {
        // 更新收藏状态
        const index = requests.value.findIndex(req => req.id === id)
        if (index !== -1) {
          requests.value[index].isFavorited = true
          requests.value[index].favoriteCount = (requests.value[index].favoriteCount || 0) + 1
        }
        ElMessage.success('收藏成功')
        return { success: true }
      }
    } catch (error) {
      console.error('收藏失败:', error)
      ElMessage.error('收藏失败')
    }
  }

  // 取消收藏
  const unfavoriteRequest = async (id) => {
    try {
      const response = await buddyRequestApi.unfavoriteRequest(id)
      if (response.success) {
        // 更新收藏状态
        const index = requests.value.findIndex(req => req.id === id)
        if (index !== -1) {
          requests.value[index].isFavorited = false
          requests.value[index].favoriteCount = Math.max((requests.value[index].favoriteCount || 0) - 1, 0)
        }
        ElMessage.success('取消收藏成功')
        return { success: true }
      }
    } catch (error) {
      console.error('取消收藏失败:', error)
      ElMessage.error('取消收藏失败')
    }
  }

  // 申请匹配
  const applyMatch = async (requestId, message = '') => {
    try {
      const response = await buddyRequestApi.applyMatch(requestId, { message })
      if (response.success) {
        ElMessage.success('申请已发送，等待对方确认')
        return { success: true, data: response.data }
      } else {
        ElMessage.error(response.message || '申请失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('申请匹配失败:', error)
      ElMessage.error('申请匹配失败')
      return { success: false, message: error.message }
    }
  }

  // 获取我的需求
  const fetchMyRequests = async (params = {}) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.getMyRequests(params)
      if (response.success) {
        myRequests.value = response.data.list
        return response.data
      }
    } catch (error) {
      console.error('获取我的需求失败:', error)
      ElMessage.error('获取我的需求失败')
    } finally {
      loading.value = false
    }
  }

  // 获取收藏的需求
  const fetchFavoriteRequests = async (params = {}) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.getFavoriteRequests(params)
      if (response.success) {
        favoriteRequests.value = response.data.list
        return response.data
      }
    } catch (error) {
      console.error('获取收藏需求失败:', error)
      ElMessage.error('获取收藏需求失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索需求
  const searchRequests = async (keyword, params = {}) => {
    loading.value = true
    try {
      const response = await buddyRequestApi.searchRequests({ keyword, ...params })
      if (response.success) {
        requests.value = response.data.list
        total.value = response.data.total
        return response.data
      }
    } catch (error) {
      console.error('搜索需求失败:', error)
      ElMessage.error('搜索需求失败')
    } finally {
      loading.value = false
    }
  }

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      typeId: null,
      keyword: '',
      sortBy: 'created_at',
      sortOrder: 'desc',
      status: 0,
      page: 1,
      size: 20
    }
  }

  // 清空当前需求
  const clearCurrentRequest = () => {
    currentRequest.value = null
  }

  return {
    // 状态
    requests,
    requestTypes,
    conditionTemplates,
    currentRequest,
    loading,
    total,
    filters,
    myRequests,
    favoriteRequests,

    // 计算属性
    activeRequests,
    expiredRequests,
    completedRequests,

    // 方法
    fetchRequestTypes,
    fetchConditionTemplates,
    fetchRequests,
    fetchRequestDetail,
    createRequest,
    updateRequest,
    cancelRequest,
    likeRequest,
    unlikeRequest,
    favoriteRequest,
    unfavoriteRequest,
    applyMatch,
    fetchMyRequests,
    fetchFavoriteRequests,
    searchRequests,
    resetFilters,
    clearCurrentRequest
  }
})
