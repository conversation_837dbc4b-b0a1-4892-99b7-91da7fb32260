import request from './request'

export const buddyRequestApi = {
  // 获取需求类型列表
  getRequestTypes() {
    return request({
      url: '/request-types',
      method: 'get'
    })
  },

  // 发布需求
  createRequest(data) {
    return request({
      url: '/buddy-requests',
      method: 'post',
      data
    })
  },

  // 获取需求列表
  getRequests(params) {
    return request({
      url: '/buddy-requests',
      method: 'get',
      params
    })
  },

  // 获取需求详情
  getRequestDetail(id) {
    return request({
      url: `/buddy-requests/${id}`,
      method: 'get'
    })
  },

  // 更新需求
  updateRequest(id, data) {
    return request({
      url: `/buddy-requests/${id}`,
      method: 'put',
      data
    })
  },

  // 取消需求
  cancelRequest(id) {
    return request({
      url: `/buddy-requests/${id}/cancel`,
      method: 'put'
    })
  },

  // 删除需求
  deleteRequest(id) {
    return request({
      url: `/buddy-requests/${id}`,
      method: 'delete'
    })
  },

  // 点赞需求
  likeRequest(id) {
    return request({
      url: `/buddy-requests/${id}/like`,
      method: 'post'
    })
  },

  // 取消点赞
  unlikeRequest(id) {
    return request({
      url: `/buddy-requests/${id}/unlike`,
      method: 'post'
    })
  },

  // 收藏需求
  favoriteRequest(id) {
    return request({
      url: `/buddy-requests/${id}/favorite`,
      method: 'post'
    })
  },

  // 取消收藏
  unfavoriteRequest(id) {
    return request({
      url: `/buddy-requests/${id}/unfavorite`,
      method: 'post'
    })
  },

  // 申请匹配
  applyMatch(requestId, data) {
    return request({
      url: `/buddy-requests/${requestId}/apply`,
      method: 'post',
      data
    })
  },

  // 获取我的需求
  getMyRequests(params) {
    return request({
      url: '/buddy-requests/my',
      method: 'get',
      params
    })
  },

  // 获取我收藏的需求
  getFavoriteRequests(params) {
    return request({
      url: '/buddy-requests/favorites',
      method: 'get',
      params
    })
  },

  // 搜索需求
  searchRequests(params) {
    return request({
      url: '/buddy-requests/search',
      method: 'get',
      params
    })
  },

  // 获取热门需求
  getHotRequests(params) {
    return request({
      url: '/buddy-requests/hot',
      method: 'get',
      params
    })
  },

  // 获取推荐需求
  getRecommendedRequests(params) {
    return request({
      url: '/buddy-requests/recommended',
      method: 'get',
      params
    })
  },

  // 获取条件模板
  getConditionTemplates() {
    return request({
      url: '/buddy-requests/condition-templates',
      method: 'get'
    })
  },

  // 获取热门需求
  getHotRequests(params) {
    return request({
      url: '/buddy-requests/hot',
      method: 'get',
      params
    })
  },

  // 获取推荐需求
  getRecommendedRequests(params) {
    return request({
      url: '/buddy-requests/recommended',
      method: 'get',
      params
    })
  },

  // 举报需求
  reportRequest(id, data) {
    return request({
      url: `/buddy-requests/${id}/report`,
      method: 'post',
      data
    })
  },

  // 获取需求统计信息
  getRequestStats() {
    return request({
      url: '/buddy-requests/stats',
      method: 'get'
    })
  },

  // 获取附加条件模板
  getConditionTemplates() {
    return request({
      url: '/buddy-requests/condition-templates',
      method: 'get'
    })
  },

  // 上传需求相关图片
  uploadRequestImage(file) {
    const formData = new FormData()
    formData.append('image', file)
    return request({
      url: '/buddy-requests/upload-image',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
