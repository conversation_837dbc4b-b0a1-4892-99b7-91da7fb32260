import request from '@/utils/request'

/**
 * 提交用户评价
 */
export function submitUserEvaluation(data) {
  return request({
    url: '/api/user/evaluation/submit',
    method: 'post',
    data
  })
}

/**
 * 获取评价标签列表
 */
export function getEvaluationTags() {
  return request({
    url: '/api/user/evaluation/tags',
    method: 'get'
  })
}

/**
 * 获取用户评价统计
 */
export function getUserEvaluationStats(userId) {
  return request({
    url: `/api/user/evaluation/stats/${userId}`,
    method: 'get'
  })
}

/**
 * 获取用户收到的评价列表
 */
export function getUserEvaluations(userId, params) {
  return request({
    url: `/api/user/evaluation/list/${userId}`,
    method: 'get',
    params
  })
}

/**
 * 获取待评价的匹配记录
 */
export function getPendingEvaluations() {
  return request({
    url: '/api/user/evaluation/pending',
    method: 'get'
  })
}
