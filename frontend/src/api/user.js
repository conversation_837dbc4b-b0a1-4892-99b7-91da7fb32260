import request from './request'

export const userApi = {
  // 用户注册
  register(data) {
    return request({
      url: '/users/register',
      method: 'post',
      data
    })
  },

  // 用户登录
  login(data) {
    return request({
      url: '/users/login',
      method: 'post',
      data
    })
  },

  // 获取用户信息
  getUserInfo(id) {
    return request({
      url: id ? `/users/${id}` : '/users/profile',
      method: 'get'
    })
  },

  // 根据学号获取用户信息
  getUserByStudentId(studentId) {
    return request({
      url: `/users/student/${studentId}`,
      method: 'get'
    })
  },

  // 更新用户信息
  updateUser(id, data) {
    return request({
      url: `/users/${id}`,
      method: 'put',
      data
    })
  },

  // 验证用户身份
  verifyUser(id) {
    return request({
      url: `/users/${id}/verify`,
      method: 'post'
    })
  },

  // 上传头像
  uploadAvatar(file) {
    const formData = new FormData()
    formData.append('avatar', file)
    return request({
      url: '/users/avatar',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 修改密码
  changePassword(data) {
    return request({
      url: '/users/password',
      method: 'put',
      data
    })
  },

  // 发送验证码
  sendVerificationCode(data) {
    return request({
      url: '/users/verification-code',
      method: 'post',
      data
    })
  },

  // 重置密码
  resetPassword(data) {
    return request({
      url: '/users/reset-password',
      method: 'post',
      data
    })
  }
}
