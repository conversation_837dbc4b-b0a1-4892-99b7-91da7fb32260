import request from '@/utils/request'

/**
 * 学号认证API
 */
export function verifyStudentId(data) {
  return request({
    url: '/api/user/verification/student',
    method: 'post',
    data
  })
}

/**
 * 人脸认证API
 */
export function verifyFace(data) {
  return request({
    url: '/api/user/verification/face',
    method: 'post',
    data
  })
}

/**
 * 检查认证状态
 */
export function checkVerificationStatus(verificationType) {
  return request({
    url: `/api/user/verification/status/${verificationType}`,
    method: 'get'
  })
}

/**
 * 获取认证记录
 */
export function getVerificationHistory() {
  return request({
    url: '/api/user/verification/history',
    method: 'get'
  })
}
