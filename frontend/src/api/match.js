import request from '@/utils/request'

/**
 * 触发手动匹配
 */
export function triggerManualMatch(data) {
  return request({
    url: '/api/match/trigger',
    method: 'post',
    data
  })
}

/**
 * 获取匹配结果
 */
export function getMatchResults(params) {
  return request({
    url: '/api/match/results',
    method: 'get',
    params
  })
}

/**
 * 接受匹配
 */
export function acceptMatch(matchId) {
  return request({
    url: `/api/match/accept/${matchId}`,
    method: 'post'
  })
}

/**
 * 拒绝匹配
 */
export function rejectMatch(matchId, reason) {
  return request({
    url: `/api/match/reject/${matchId}`,
    method: 'post',
    data: { reason }
  })
}

/**
 * 获取匹配历史记录
 */
export function getMatchHistory(params) {
  return request({
    url: '/api/match/history',
    method: 'get',
    params
  })
}

/**
 * 获取匹配统计数据
 */
export function getMatchStats() {
  return request({
    url: '/api/match/stats',
    method: 'get'
  })
}

/**
 * 保存匹配偏好设置
 */
export function saveMatchPreferences(data) {
  return request({
    url: '/api/match/preferences',
    method: 'post',
    data
  })
}

/**
 * 获取匹配偏好设置
 */
export function getMatchPreferences() {
  return request({
    url: '/api/match/preferences',
    method: 'get'
  })
}

/**
 * 获取匹配规则配置
 */
export function getMatchRules() {
  return request({
    url: '/api/match/rules',
    method: 'get'
  })
}

/**
 * 开始活动
 */
export function startActivity(matchId) {
  return request({
    url: `/api/match/activity/start/${matchId}`,
    method: 'post'
  })
}

/**
 * 结束活动
 */
export function endActivity(matchId, data) {
  return request({
    url: `/api/match/activity/end/${matchId}`,
    method: 'post',
    data
  })
}
