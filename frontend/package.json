{"name": "campus-buddy-frontend", "version": "1.0.0", "description": "校园搭子即时匹配系统前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "vuedraggable": "^4.1.0", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2"}, "keywords": ["vue3", "element-plus", "campus", "social", "matching"], "author": "CampusBuddy Team", "license": "MIT"}