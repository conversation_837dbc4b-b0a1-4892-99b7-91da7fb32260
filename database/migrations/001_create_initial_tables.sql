-- 迁移脚本：创建初始表结构
-- 版本：v1.0.0
-- 创建时间：2024-01-01
-- 描述：创建校园搭子系统的基础表结构

-- 检查数据库是否存在
CREATE DATABASE IF NOT EXISTS campus_buddy DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE campus_buddy;

-- 创建版本控制表
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(255) PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 记录当前迁移版本
INSERT IGNORE INTO schema_migrations (version) VALUES ('001_create_initial_tables');

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    student_id VARCHAR(20) NOT NULL UNIQUE COMMENT '学号',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    avatar VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    college VARCHAR(100) COMMENT '学院',
    major VARCHAR(100) COMMENT '专业',
    grade VARCHAR(10) COMMENT '年级',
    bio TEXT COMMENT '个人简介',
    interests JSON COMMENT '兴趣爱好',
    location JSON COMMENT '常用位置',
    credit_score INT DEFAULT 100 COMMENT '信用分数',
    status TINYINT DEFAULT 0 COMMENT '账户状态：0-正常，1-冻结，2-注销',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已认证',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_student_id (student_id),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_college_major (college, major),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 需求类型表
CREATE TABLE IF NOT EXISTS request_types (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '类型ID',
    name VARCHAR(50) NOT NULL COMMENT '类型名称',
    icon VARCHAR(100) COMMENT '图标',
    description TEXT COMMENT '描述',
    color VARCHAR(20) COMMENT '颜色',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求类型表';

-- 搭子需求表
CREATE TABLE IF NOT EXISTS buddy_requests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '需求ID',
    user_id BIGINT NOT NULL COMMENT '发布用户ID',
    type_id INT NOT NULL COMMENT '需求类型ID',
    title VARCHAR(200) NOT NULL COMMENT '需求标题',
    description TEXT COMMENT '需求描述',
    location JSON COMMENT '位置信息',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    max_participants INT DEFAULT 1 COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前参与人数',
    gender_preference TINYINT DEFAULT 0 COMMENT '性别偏好：0-无偏好，1-男，2-女',
    grade_preference JSON COMMENT '年级偏好',
    college_preference JSON COMMENT '学院偏好',
    tags JSON COMMENT '标签',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待匹配，1-匹配中，2-已完成，3-已取消，4-已过期',
    priority INT DEFAULT 0 COMMENT '优先级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (type_id) REFERENCES request_types(id),
    INDEX idx_user_id (user_id),
    INDEX idx_type_id (type_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搭子需求表';

-- 匹配记录表
CREATE TABLE IF NOT EXISTS matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配ID',
    request_id BIGINT NOT NULL COMMENT '需求ID',
    requester_id BIGINT NOT NULL COMMENT '发起者ID',
    responder_id BIGINT NOT NULL COMMENT '响应者ID',
    match_score DECIMAL(5,2) COMMENT '匹配分数',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待确认，1-已确认，2-已拒绝，3-已完成，4-已取消',
    confirmed_at TIMESTAMP NULL COMMENT '确认时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    rating_by_requester TINYINT COMMENT '发起者评分(1-5)',
    rating_by_responder TINYINT COMMENT '响应者评分(1-5)',
    comment_by_requester TEXT COMMENT '发起者评价',
    comment_by_responder TEXT COMMENT '响应者评价',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (request_id) REFERENCES buddy_requests(id),
    FOREIGN KEY (requester_id) REFERENCES users(id),
    FOREIGN KEY (responder_id) REFERENCES users(id),
    INDEX idx_request_id (request_id),
    INDEX idx_requester_id (requester_id),
    INDEX idx_responder_id (responder_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_request_responder (request_id, responder_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配记录表';
