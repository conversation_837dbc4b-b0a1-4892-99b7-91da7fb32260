-- 校园搭子即时匹配系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS campus_buddy DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE campus_buddy;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    student_id VARCHAR(20) NOT NULL UNIQUE COMMENT '学号',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    avatar VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    college VARCHAR(100) COMMENT '学院',
    major VARCHAR(100) COMMENT '专业',
    grade VARCHAR(10) COMMENT '年级',
    bio TEXT COMMENT '个人简介',
    interests JSON COMMENT '兴趣爱好',
    location JSON COMMENT '常用位置',
    credit_score INT DEFAULT 100 COMMENT '信用分数',
    status TINYINT DEFAULT 0 COMMENT '账户状态：0-正常，1-冻结，2-注销',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已认证',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_student_id (student_id),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_college_major (college, major),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 需求类型表
CREATE TABLE IF NOT EXISTS request_types (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '类型ID',
    name VARCHAR(50) NOT NULL COMMENT '类型名称',
    icon VARCHAR(100) COMMENT '图标',
    description TEXT COMMENT '描述',
    color VARCHAR(20) COMMENT '颜色',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求类型表';

-- 插入默认需求类型
INSERT INTO request_types (name, icon, description, color, sort_order) VALUES
('学习搭子', 'reading', '一起学习、讨论学术问题', '#409eff', 1),
('运动搭子', 'basketball', '一起运动健身', '#67c23a', 2),
('饭友', 'food', '一起用餐聊天', '#e6a23c', 3),
('图书馆搭子', 'library', '一起去图书馆学习', '#909399', 4),
('购物搭子', 'shopping', '一起购物逛街', '#f56c6c', 5),
('旅游搭子', 'travel', '一起旅游出行', '#9c27b0', 6),
('游戏搭子', 'game', '一起玩游戏', '#ff9800', 7),
('其他', 'more', '其他类型的搭子需求', '#607d8b', 8);

-- 搭子需求表
CREATE TABLE IF NOT EXISTS buddy_requests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '需求ID',
    user_id BIGINT NOT NULL COMMENT '发布用户ID',
    type_id INT NOT NULL COMMENT '需求类型ID',
    title VARCHAR(200) NOT NULL COMMENT '需求标题',
    description TEXT COMMENT '需求描述',
    location JSON COMMENT '位置信息',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    max_participants INT DEFAULT 1 COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前参与人数',
    gender_preference TINYINT DEFAULT 0 COMMENT '性别偏好：0-无偏好，1-男，2-女',
    grade_preference JSON COMMENT '年级偏好',
    college_preference JSON COMMENT '学院偏好',
    tags JSON COMMENT '标签',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待匹配，1-匹配中，2-已完成，3-已取消，4-已过期',
    priority INT DEFAULT 0 COMMENT '优先级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (type_id) REFERENCES request_types(id),
    INDEX idx_user_id (user_id),
    INDEX idx_type_id (type_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_location (location),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搭子需求表';

-- 匹配记录表
CREATE TABLE IF NOT EXISTS matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配ID',
    request_id BIGINT NOT NULL COMMENT '需求ID',
    requester_id BIGINT NOT NULL COMMENT '发起者ID',
    responder_id BIGINT NOT NULL COMMENT '响应者ID',
    match_score DECIMAL(5,2) COMMENT '匹配分数',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待确认，1-已确认，2-已拒绝，3-已完成，4-已取消',
    confirmed_at TIMESTAMP NULL COMMENT '确认时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    rating_by_requester TINYINT COMMENT '发起者评分(1-5)',
    rating_by_responder TINYINT COMMENT '响应者评分(1-5)',
    comment_by_requester TEXT COMMENT '发起者评价',
    comment_by_responder TEXT COMMENT '响应者评价',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (request_id) REFERENCES buddy_requests(id),
    FOREIGN KEY (requester_id) REFERENCES users(id),
    FOREIGN KEY (responder_id) REFERENCES users(id),
    INDEX idx_request_id (request_id),
    INDEX idx_requester_id (requester_id),
    INDEX idx_responder_id (responder_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_request_responder (request_id, responder_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配记录表';

-- 消息表
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    match_id BIGINT NOT NULL COMMENT '匹配ID',
    sender_id BIGINT NOT NULL COMMENT '发送者ID',
    receiver_id BIGINT NOT NULL COMMENT '接收者ID',
    content TEXT NOT NULL COMMENT '消息内容',
    message_type TINYINT DEFAULT 0 COMMENT '消息类型：0-文本，1-图片，2-语音，3-系统消息',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (match_id) REFERENCES matches(id),
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (receiver_id) REFERENCES users(id),
    INDEX idx_match_id (match_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 举报表
CREATE TABLE IF NOT EXISTS reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '举报ID',
    reporter_id BIGINT NOT NULL COMMENT '举报者ID',
    reported_id BIGINT NOT NULL COMMENT '被举报者ID',
    match_id BIGINT COMMENT '相关匹配ID',
    reason VARCHAR(200) NOT NULL COMMENT '举报原因',
    description TEXT COMMENT '详细描述',
    evidence JSON COMMENT '证据（图片等）',
    status TINYINT DEFAULT 0 COMMENT '处理状态：0-待处理，1-处理中，2-已处理，3-已驳回',
    handler_id BIGINT COMMENT '处理人ID',
    handle_result TEXT COMMENT '处理结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (reporter_id) REFERENCES users(id),
    FOREIGN KEY (reported_id) REFERENCES users(id),
    FOREIGN KEY (match_id) REFERENCES matches(id),
    INDEX idx_reporter_id (reporter_id),
    INDEX idx_reported_id (reported_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='举报表';

-- 系统通知表
CREATE TABLE IF NOT EXISTS notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通知ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type TINYINT DEFAULT 0 COMMENT '通知类型：0-系统通知，1-匹配通知，2-消息通知，3-评价通知',
    related_id BIGINT COMMENT '相关ID（如匹配ID、消息ID等）',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统通知表';

-- 用户标签表
CREATE TABLE IF NOT EXISTS user_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
    tag_type TINYINT DEFAULT 0 COMMENT '标签类型：0-兴趣，1-技能，2-性格',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_tag_type (tag_type),
    UNIQUE KEY uk_user_tag (user_id, tag_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户标签表';

-- 黑名单表
CREATE TABLE IF NOT EXISTS blacklist (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    blocked_user_id BIGINT NOT NULL COMMENT '被拉黑用户ID',
    reason VARCHAR(200) COMMENT '拉黑原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (blocked_user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_blocked_user_id (blocked_user_id),
    UNIQUE KEY uk_user_blocked (user_id, blocked_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='黑名单表';

-- 用户认证记录表
CREATE TABLE IF NOT EXISTS user_verifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '认证ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    verification_type VARCHAR(50) NOT NULL COMMENT '认证类型：STUDENT_ID, FACE',
    verification_data TEXT COMMENT '认证数据',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '认证状态：PENDING, SUCCESS, FAILED, ERROR',
    api_response TEXT COMMENT 'API响应数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_type (user_id, verification_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户认证记录表';

-- 匹配规则表
CREATE TABLE IF NOT EXISTS match_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型：LOCATION, TIME, INTEREST, GRADE',
    rule_config JSON COMMENT '规则配置',
    priority INT DEFAULT 1 COMMENT '优先级',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配规则表';

-- 匹配记录表（新版本）
CREATE TABLE IF NOT EXISTS match_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配记录ID',
    request_id BIGINT NOT NULL COMMENT '需求ID',
    requester_id BIGINT NOT NULL COMMENT '发起者ID',
    matched_user_id BIGINT NOT NULL COMMENT '匹配用户ID',
    match_score DECIMAL(5,2) COMMENT '匹配分数',
    match_reason TEXT COMMENT '匹配原因',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING, ACCEPTED, REJECTED, COMPLETED',
    requester_action VARCHAR(20) COMMENT '发起者操作：ACCEPT, REJECT',
    matched_user_action VARCHAR(20) COMMENT '匹配者操作：ACCEPT, REJECT',
    activity_start_time TIMESTAMP NULL COMMENT '活动开始时间',
    activity_end_time TIMESTAMP NULL COMMENT '活动结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (request_id) REFERENCES buddy_requests(id),
    FOREIGN KEY (requester_id) REFERENCES users(id),
    FOREIGN KEY (matched_user_id) REFERENCES users(id),
    INDEX idx_request (request_id),
    INDEX idx_requester (requester_id),
    INDEX idx_matched_user (matched_user_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配记录表（新版本）';

-- 评价标签表
CREATE TABLE IF NOT EXISTS evaluation_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
    tag_category VARCHAR(20) NOT NULL COMMENT '标签分类：POSITIVE, NEUTRAL, NEGATIVE',
    tag_icon VARCHAR(50) COMMENT '标签图标',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_tag_name (tag_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价标签表';

-- 用户评价表
CREATE TABLE IF NOT EXISTS user_evaluations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID',
    match_record_id BIGINT NOT NULL COMMENT '匹配记录ID',
    evaluator_id BIGINT NOT NULL COMMENT '评价者ID',
    evaluated_user_id BIGINT NOT NULL COMMENT '被评价者ID',
    overall_rating INT NOT NULL COMMENT '总体评分 1-5',
    punctuality_rating INT NOT NULL COMMENT '准时性评分 1-5',
    communication_rating INT NOT NULL COMMENT '沟通能力评分 1-5',
    cooperation_rating INT NOT NULL COMMENT '合作能力评分 1-5',
    evaluation_tags JSON COMMENT '评价标签ID列表',
    evaluation_text TEXT COMMENT '评价文字',
    is_anonymous BOOLEAN DEFAULT TRUE COMMENT '是否匿名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (match_record_id) REFERENCES match_records(id),
    FOREIGN KEY (evaluator_id) REFERENCES users(id),
    FOREIGN KEY (evaluated_user_id) REFERENCES users(id),
    INDEX idx_match_record (match_record_id),
    INDEX idx_evaluator (evaluator_id),
    INDEX idx_evaluated_user (evaluated_user_id),
    UNIQUE KEY uk_match_evaluator (match_record_id, evaluator_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户评价表';

-- 插入测试数据
INSERT INTO users (username, password, email, nickname, avatar, phone, college, major, grade, gender, created_at, updated_at) VALUES
('test_user1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdOIGgRkOmkNjKSO', '<EMAIL>', '张三', '/avatars/user1.jpg', '13800138001', '计算机学院', '软件工程', '2022级', 'MALE', NOW(), NOW()),
('test_user2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdOIGgRkOmkNjKSO', '<EMAIL>', '李四', '/avatars/user2.jpg', '13800138002', '电子工程学院', '电子信息工程', '2023级', 'FEMALE', NOW(), NOW()),
('test_user3', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdOIGgRkOmkNjKSO', '<EMAIL>', '王五', '/avatars/user3.jpg', '13800138003', '机械工程学院', '机械设计制造及其自动化', '2021级', 'MALE', NOW(), NOW());

-- 插入评价标签测试数据
INSERT INTO evaluation_tags (tag_name, tag_category, tag_icon, is_active) VALUES
('准时王者', 'POSITIVE', 'Clock', TRUE),
('话题达人', 'POSITIVE', 'ChatDotRound', TRUE),
('学习专注', 'POSITIVE', 'Star', TRUE),
('干饭神速', 'POSITIVE', 'Trophy', TRUE),
('运动健将', 'POSITIVE', 'Trophy', TRUE),
('贴心暖人', 'POSITIVE', 'UserFilled', TRUE),
('安静内向', 'NEUTRAL', 'UserFilled', TRUE),
('活泼外向', 'NEUTRAL', 'UserFilled', TRUE),
('慢热型', 'NEUTRAL', 'UserFilled', TRUE),
('效率至上', 'NEUTRAL', 'Star', TRUE);

-- 插入匹配规则测试数据
INSERT INTO match_rules (rule_name, rule_type, rule_config, priority, is_active) VALUES
('同校区优先', 'LOCATION', '{"campus": "same", "weight": 0.3}', 1, TRUE),
('时间匹配', 'TIME', '{"time_overlap": 0.7, "weight": 0.25}', 2, TRUE),
('兴趣相似', 'INTEREST', '{"similarity_threshold": 0.6, "weight": 0.25}', 3, TRUE),
('年级偏好', 'GRADE', '{"grade_difference": 2, "weight": 0.2}', 4, TRUE);
