# 数据库配置文件
database:
  # 主数据库配置
  primary:
    host: localhost
    port: 3306
    database: campus_buddy
    username: root
    password: 123456
    charset: utf8mb4
    collation: utf8mb4_unicode_ci
    timezone: Asia/Shanghai
    
    # 连接池配置
    pool:
      initial_size: 5
      min_idle: 5
      max_active: 20
      max_wait: 60000
      time_between_eviction_runs_millis: 60000
      min_evictable_idle_time_millis: 300000
      validation_query: SELECT 1
      test_while_idle: true
      test_on_borrow: false
      test_on_return: false
      
  # 读库配置（如果需要读写分离）
  read:
    host: localhost
    port: 3306
    database: campus_buddy
    username: root
    password: 123456
    charset: utf8mb4
    collation: utf8mb4_unicode_ci
    timezone: Asia/Shanghai

# Redis配置
redis:
  host: localhost
  port: 6379
  password: ""
  database: 0
  timeout: 3000
  
  # 连接池配置
  pool:
    max_active: 8
    max_idle: 8
    min_idle: 0
    max_wait: -1

# 数据库表前缀
table_prefix: ""

# 数据库版本
version: "8.0"

# 备份配置
backup:
  enabled: true
  path: "./backup"
  schedule: "0 2 * * *"  # 每天凌晨2点备份
  retention_days: 30

# 监控配置
monitoring:
  enabled: true
  slow_query_threshold: 1000  # 慢查询阈值（毫秒）
  log_queries: false  # 是否记录所有查询
