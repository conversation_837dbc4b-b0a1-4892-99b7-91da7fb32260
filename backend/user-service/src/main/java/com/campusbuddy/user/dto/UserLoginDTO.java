package com.campusbuddy.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用户登录DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Schema(description = "用户登录请求")
public class UserLoginDTO {

    @NotBlank(message = "学号不能为空")
    @Schema(description = "学号", example = "20210001")
    private String studentId;

    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码", example = "Password123")
    private String password;

    @Schema(description = "记住我", example = "true")
    private Boolean rememberMe = false;
}
