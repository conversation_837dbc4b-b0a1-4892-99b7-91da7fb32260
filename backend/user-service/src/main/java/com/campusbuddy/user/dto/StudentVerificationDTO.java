package com.campusbuddy.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 学号认证DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Schema(description = "学号认证请求")
public class StudentVerificationDTO {

    @NotBlank(message = "学号不能为空")
    @Pattern(regexp = "^[0-9]{8,12}$", message = "学号格式不正确")
    @Schema(description = "学号", example = "202012345678")
    private String studentId;

    @NotBlank(message = "校园网密码不能为空")
    @Schema(description = "校园网密码")
    private String campusPassword;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "学院")
    private String college;

    @Schema(description = "专业")
    private String major;

    @Schema(description = "年级")
    private String grade;
}
