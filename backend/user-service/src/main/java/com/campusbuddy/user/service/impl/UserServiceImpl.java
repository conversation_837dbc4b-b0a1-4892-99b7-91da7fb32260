package com.campusbuddy.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.campusbuddy.user.dto.UserLoginDTO;
import com.campusbuddy.user.dto.UserRegisterDTO;
import com.campusbuddy.user.entity.User;
import com.campusbuddy.user.mapper.UserMapper;
import com.campusbuddy.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User register(UserRegisterDTO registerDTO) {
        // 验证密码一致性
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        // 检查学号是否已存在
        if (findByStudentId(registerDTO.getStudentId()) != null) {
            throw new RuntimeException("学号已存在");
        }

        // 检查邮箱是否已存在
        if (registerDTO.getEmail() != null && findByEmail(registerDTO.getEmail()) != null) {
            throw new RuntimeException("邮箱已存在");
        }

        // 检查手机号是否已存在
        if (registerDTO.getPhone() != null && findByPhone(registerDTO.getPhone()) != null) {
            throw new RuntimeException("手机号已存在");
        }

        // 创建用户对象
        User user = new User();
        BeanUtil.copyProperties(registerDTO, user);
        
        // 加密密码
        user.setPassword(BCrypt.hashpw(registerDTO.getPassword(), BCrypt.gensalt()));
        
        // 设置默认值
        user.setCreditScore(100);
        user.setStatus(0);
        user.setIsVerified(false);
        user.setDeleted(false);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        // 保存用户
        if (save(user)) {
            log.info("用户注册成功，学号：{}", user.getStudentId());
            return user;
        } else {
            throw new RuntimeException("用户注册失败");
        }
    }

    @Override
    public String login(UserLoginDTO loginDTO) {
        // 根据学号查询用户
        User user = findByStudentId(loginDTO.getStudentId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证密码
        if (!BCrypt.checkpw(loginDTO.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 检查账户状态
        if (user.getStatus() != 0) {
            throw new RuntimeException("账户已被冻结或注销");
        }

        // 生成JWT Token（这里简化处理，实际应该使用JWT工具类）
        // TODO: 实现JWT Token生成逻辑
        String token = "jwt_token_" + user.getId() + "_" + System.currentTimeMillis();
        
        log.info("用户登录成功，学号：{}", user.getStudentId());
        return token;
    }

    @Override
    public User findByStudentId(String studentId) {
        return userMapper.findByStudentId(studentId);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(User user) {
        user.setUpdatedAt(LocalDateTime.now());
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyUser(Long userId) {
        User user = getById(userId);
        if (user != null) {
            user.setIsVerified(true);
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCreditScore(Long userId, Integer score) {
        User user = getById(userId);
        if (user != null) {
            int newScore = user.getCreditScore() + score;
            // 信用分数范围：0-200
            newScore = Math.max(0, Math.min(200, newScore));
            user.setCreditScore(newScore);
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        }
        return false;
    }
}
