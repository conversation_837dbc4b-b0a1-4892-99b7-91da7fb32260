package com.campusbuddy.user.service;

import com.campusbuddy.user.dto.EvaluationSubmitDTO;
import com.campusbuddy.user.entity.EvaluationTag;
import com.campusbuddy.user.entity.UserEvaluation;
import com.campusbuddy.common.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 评价服务接口
 */
public interface EvaluationService {

    /**
     * 提交用户评价
     */
    Result<Void> submitEvaluation(Long evaluatorId, EvaluationSubmitDTO dto);

    /**
     * 获取评价标签列表
     */
    Result<List<EvaluationTag>> getEvaluationTags();

    /**
     * 获取用户评价统计
     */
    Result<Map<String, Object>> getUserEvaluationStats(Long userId);

    /**
     * 获取用户收到的评价列表
     */
    Result<Map<String, Object>> getUserEvaluations(Long userId, Integer page, Integer size);

    /**
     * 获取待评价的匹配记录
     */
    Result<List<Map<String, Object>>> getPendingEvaluations(Long userId);

    /**
     * 检查是否可以评价指定用户
     */
    Result<Boolean> canEvaluateUser(Long evaluatorId, Long evaluatedUserId, Long matchRecordId);
}
