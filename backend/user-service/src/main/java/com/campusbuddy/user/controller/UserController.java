package com.campusbuddy.user.controller;

import com.campusbuddy.user.dto.UserLoginDTO;
import com.campusbuddy.user.dto.UserRegisterDTO;
import com.campusbuddy.user.entity.User;
import com.campusbuddy.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户注册、登录、信息管理等接口")
public class UserController {

    private final UserService userService;

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "学生用户注册接口")
    public ResponseEntity<Map<String, Object>> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        Map<String, Object> result = new HashMap<>();
        try {
            User user = userService.register(registerDTO);
            result.put("success", true);
            result.put("message", "注册成功");
            result.put("data", user);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("用户注册失败：{}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "学生用户登录接口")
    public ResponseEntity<Map<String, Object>> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        Map<String, Object> result = new HashMap<>();
        try {
            String token = userService.login(loginDTO);
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("token", token);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("用户登录失败：{}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    public ResponseEntity<Map<String, Object>> getUserById(
            @Parameter(description = "用户ID") @PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            User user = userService.getById(id);
            if (user != null) {
                // 隐藏敏感信息
                user.setPassword(null);
                result.put("success", true);
                result.put("data", user);
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取用户信息失败：{}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @GetMapping("/student/{studentId}")
    @Operation(summary = "根据学号获取用户信息", description = "根据学号获取用户详细信息")
    public ResponseEntity<Map<String, Object>> getUserByStudentId(
            @Parameter(description = "学号") @PathVariable String studentId) {
        Map<String, Object> result = new HashMap<>();
        try {
            User user = userService.findByStudentId(studentId);
            if (user != null) {
                // 隐藏敏感信息
                user.setPassword(null);
                result.put("success", true);
                result.put("data", user);
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取用户信息失败：{}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新用户信息", description = "更新用户详细信息")
    public ResponseEntity<Map<String, Object>> updateUser(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @RequestBody User user) {
        Map<String, Object> result = new HashMap<>();
        try {
            user.setId(id);
            boolean success = userService.updateUser(user);
            if (success) {
                result.put("success", true);
                result.put("message", "更新成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("更新用户信息失败：{}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @PostMapping("/{id}/verify")
    @Operation(summary = "验证用户身份", description = "验证用户身份认证")
    public ResponseEntity<Map<String, Object>> verifyUser(
            @Parameter(description = "用户ID") @PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = userService.verifyUser(id);
            if (success) {
                result.put("success", true);
                result.put("message", "验证成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "验证失败");
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("验证用户身份失败：{}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
}
