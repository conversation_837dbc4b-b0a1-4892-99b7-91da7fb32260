package com.campusbuddy.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户注册DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Schema(description = "用户注册请求")
public class UserRegisterDTO {

    @NotBlank(message = "学号不能为空")
    @Pattern(regexp = "^[0-9]{8,12}$", message = "学号格式不正确")
    @Schema(description = "学号", example = "20210001")
    private String studentId;

    @NotBlank(message = "用户名不能为空")
    @Schema(description = "用户名", example = "张三")
    private String username;

    @NotBlank(message = "真实姓名不能为空")
    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @NotBlank(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$", 
             message = "密码至少8位，包含大小写字母和数字")
    @Schema(description = "密码", example = "Password123")
    private String password;

    @NotBlank(message = "确认密码不能为空")
    @Schema(description = "确认密码", example = "Password123")
    private String confirmPassword;

    @Schema(description = "学院", example = "计算机科学与技术学院")
    private String college;

    @Schema(description = "专业", example = "软件工程")
    private String major;

    @Schema(description = "年级", example = "2021")
    private String grade;

    @Schema(description = "性别：1-男，2-女", example = "1")
    private Integer gender;

    @Schema(description = "验证码", example = "123456")
    private String verificationCode;
}
