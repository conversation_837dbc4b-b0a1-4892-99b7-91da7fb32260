package com.campusbuddy.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.campusbuddy.user.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据学号查询用户
     * 
     * @param studentId 学号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE student_id = #{studentId} AND deleted = 0")
    User findByStudentId(@Param("studentId") String studentId);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted = 0")
    User findByPhone(@Param("phone") String phone);
}
