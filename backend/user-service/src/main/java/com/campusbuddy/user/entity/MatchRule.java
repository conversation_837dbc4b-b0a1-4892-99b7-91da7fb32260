package com.campusbuddy.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 匹配规则实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("match_rules")
@Schema(description = "匹配规则配置")
public class MatchRule {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "规则ID")
    private Long id;

    @TableField("rule_name")
    @Schema(description = "规则名称")
    private String ruleName;

    @TableField("rule_type")
    @Schema(description = "规则类型：1-需求类型匹配，2-校区匹配，3-时间匹配，4-相似度匹配")
    private Integer ruleType;

    @TableField("rule_config")
    @Schema(description = "规则配置（JSON格式）")
    private String ruleConfig;

    @TableField("priority")
    @Schema(description = "优先级（数值越大优先级越高）")
    private Integer priority;

    @TableField("similarity_threshold")
    @Schema(description = "相似度阈值（0-100）")
    private Integer similarityThreshold;

    @TableField("max_candidates")
    @Schema(description = "最大候选数量")
    private Integer maxCandidates;

    @TableField("time_range_hours")
    @Schema(description = "时间范围（小时）")
    private Integer timeRangeHours;

    @TableField("is_active")
    @Schema(description = "是否启用")
    private Boolean isActive;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;
}
