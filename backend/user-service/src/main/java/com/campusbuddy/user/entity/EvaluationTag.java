package com.campusbuddy.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 评价标签实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("evaluation_tags")
@Schema(description = "评价标签")
public class EvaluationTag {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "标签ID")
    private Long id;

    @TableField("tag_name")
    @Schema(description = "标签名称")
    private String tagName;

    @TableField("tag_category")
    @Schema(description = "标签分类：1-正向标签，2-中性标签，3-负向标签")
    private Integer tagCategory;

    @TableField("tag_color")
    @Schema(description = "标签颜色")
    private String tagColor;

    @TableField("tag_icon")
    @Schema(description = "标签图标")
    private String tagIcon;

    @TableField("description")
    @Schema(description = "标签描述")
    private String description;

    @TableField("sort_order")
    @Schema(description = "排序顺序")
    private Integer sortOrder;

    @TableField("is_active")
    @Schema(description = "是否启用")
    private Boolean isActive;

    @TableField("usage_count")
    @Schema(description = "使用次数")
    private Integer usageCount;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;
}
