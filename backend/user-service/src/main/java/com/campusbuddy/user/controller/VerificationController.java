package com.campusbuddy.user.controller;

import com.campusbuddy.user.dto.FaceVerificationDTO;
import com.campusbuddy.user.dto.StudentVerificationDTO;
import com.campusbuddy.user.service.VerificationService;
import com.campusbuddy.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
 * 身份认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user/verification")
@RequiredArgsConstructor
@Validated
@Tag(name = "身份认证", description = "学号认证和人脸认证相关接口")
public class VerificationController {

    private final VerificationService verificationService;

    @PostMapping("/student")
    @Operation(summary = "学号认证", description = "通过学号和校园密码进行身份认证")
    public Result<Map<String, Object>> verifyStudentId(
            @Valid @RequestBody StudentVerificationDTO dto,
            HttpServletRequest request) {
        
        // 从请求中获取用户ID（实际项目中应该从JWT token中获取）
        Long userId = getCurrentUserId(request);
        
        log.info("收到学号认证请求，用户ID: {}, 学号: {}", userId, dto.getStudentId());
        
        return verificationService.verifyStudentId(userId, dto);
    }

    @PostMapping("/face")
    @Operation(summary = "人脸认证", description = "通过人脸图像进行身份认证")
    public Result<Map<String, Object>> verifyFace(
            @Valid @RequestBody FaceVerificationDTO dto,
            HttpServletRequest request) {
        
        Long userId = getCurrentUserId(request);
        
        log.info("收到人脸认证请求，用户ID: {}, 操作类型: {}", userId, dto.getOperationType());
        
        return verificationService.verifyFace(userId, dto);
    }

    @GetMapping("/status/{verificationType}")
    @Operation(summary = "获取认证状态", description = "获取指定类型的认证状态")
    public Result<Map<String, Object>> getVerificationStatus(
            @Parameter(description = "认证类型", example = "STUDENT_ID")
            @PathVariable String verificationType,
            HttpServletRequest request) {
        
        Long userId = getCurrentUserId(request);
        
        log.info("获取认证状态，用户ID: {}, 认证类型: {}", userId, verificationType);
        
        return verificationService.getVerificationStatus(userId, verificationType);
    }

    @GetMapping("/history")
    @Operation(summary = "获取认证历史", description = "获取用户的认证历史记录")
    public Result<Map<String, Object>> getVerificationHistory(HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        
        log.info("获取认证历史，用户ID: {}", userId);
        
        // TODO: 实现获取认证历史的逻辑
        return Result.success("功能开发中");
    }

    /**
     * 从请求中获取当前用户ID
     * 实际项目中应该从JWT token中解析
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        // 这里是模拟实现，实际应该从JWT token中获取
        String userIdHeader = request.getHeader("X-User-Id");
        if (userIdHeader != null) {
            try {
                return Long.parseLong(userIdHeader);
            } catch (NumberFormatException e) {
                log.warn("无效的用户ID格式: {}", userIdHeader);
            }
        }
        
        // 默认返回测试用户ID
        return 1L;
    }
}
