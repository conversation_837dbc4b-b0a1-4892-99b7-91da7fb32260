package com.campusbuddy.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 匹配记录实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("match_records")
@Schema(description = "匹配记录")
public class MatchRecord {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "匹配记录ID")
    private Long id;

    @TableField("request_id")
    @Schema(description = "需求ID")
    private Long requestId;

    @TableField("requester_id")
    @Schema(description = "发起者用户ID")
    private Long requesterId;

    @TableField("matched_user_id")
    @Schema(description = "匹配用户ID")
    private Long matchedUserId;

    @TableField("match_score")
    @Schema(description = "匹配分数")
    private Double matchScore;

    @TableField("match_reason")
    @Schema(description = "匹配原因（JSON格式）")
    private String matchReason;

    @TableField("match_status")
    @Schema(description = "匹配状态：0-待确认，1-已接受，2-已拒绝，3-已完成，4-已取消")
    private Integer matchStatus;

    @TableField("requester_action")
    @Schema(description = "发起者操作：0-无操作，1-接受，2-拒绝")
    private Integer requesterAction;

    @TableField("matched_user_action")
    @Schema(description = "匹配用户操作：0-无操作，1-接受，2-拒绝")
    private Integer matchedUserAction;

    @TableField("activity_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "活动开始时间")
    private LocalDateTime activityStartTime;

    @TableField("activity_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "活动结束时间")
    private LocalDateTime activityEndTime;

    @TableField("completion_status")
    @Schema(description = "完成状态：0-未完成，1-已完成，2-提前结束")
    private Integer completionStatus;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;
}
