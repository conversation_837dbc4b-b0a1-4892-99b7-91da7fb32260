package com.campusbuddy.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 评价提交DTO
 */
@Data
@Schema(description = "评价提交请求")
public class EvaluationSubmitDTO {

    @NotNull(message = "匹配记录ID不能为空")
    @Schema(description = "匹配记录ID", example = "1")
    private Long matchRecordId;

    @NotNull(message = "被评价用户ID不能为空")
    @Schema(description = "被评价用户ID", example = "2")
    private Long evaluatedUserId;

    @NotNull(message = "总体评分不能为空")
    @Min(value = 1, message = "总体评分不能小于1")
    @Max(value = 5, message = "总体评分不能大于5")
    @Schema(description = "总体评分", example = "4")
    private Integer overallRating;

    @NotNull(message = "准时性评分不能为空")
    @Min(value = 1, message = "准时性评分不能小于1")
    @Max(value = 5, message = "准时性评分不能大于5")
    @Schema(description = "准时性评分", example = "5")
    private Integer punctualityRating;

    @NotNull(message = "沟通能力评分不能为空")
    @Min(value = 1, message = "沟通能力评分不能小于1")
    @Max(value = 5, message = "沟通能力评分不能大于5")
    @Schema(description = "沟通能力评分", example = "4")
    private Integer communicationRating;

    @NotNull(message = "合作能力评分不能为空")
    @Min(value = 1, message = "合作能力评分不能小于1")
    @Max(value = 5, message = "合作能力评分不能大于5")
    @Schema(description = "合作能力评分", example = "4")
    private Integer cooperationRating;

    @NotEmpty(message = "评价标签不能为空")
    @Size(min = 1, max = 3, message = "评价标签数量应在1-3个之间")
    @Schema(description = "评价标签ID列表", example = "[1, 2, 3]")
    private List<Long> evaluationTags;

    @Size(max = 100, message = "评价文字不能超过100字")
    @Schema(description = "评价文字", example = "这位搭子很准时，沟通也很愉快")
    private String evaluationText;

    @NotNull(message = "是否匿名不能为空")
    @Schema(description = "是否匿名评价", example = "true")
    private Boolean isAnonymous;
}
