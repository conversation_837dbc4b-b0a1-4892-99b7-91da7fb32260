package com.campusbuddy.user.service;

import com.campusbuddy.user.dto.MatchResultDTO;
import com.campusbuddy.user.dto.MatchPreferenceDTO;
import com.campusbuddy.user.entity.MatchRecord;
import com.campusbuddy.user.entity.BuddyRequest;
import java.util.List;

/**
 * 匹配服务接口
 */
public interface MatchingService {
    
    /**
     * 执行自动匹配
     * @param requestId 需求ID
     * @return 匹配结果列表
     */
    List<MatchResultDTO> performAutoMatch(Long requestId);
    
    /**
     * 手动触发匹配
     * @param userId 用户ID
     * @param requestId 需求ID
     * @return 匹配结果列表
     */
    List<MatchResultDTO> performManualMatch(Long userId, Long requestId);
    
    /**
     * 获取用户的匹配结果
     * @param userId 用户ID
     * @return 匹配结果列表
     */
    List<MatchResultDTO> getUserMatchResults(Long userId);
    
    /**
     * 接受匹配
     * @param matchRecordId 匹配记录ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean acceptMatch(Long matchRecordId, Long userId);
    
    /**
     * 拒绝匹配
     * @param matchRecordId 匹配记录ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean rejectMatch(Long matchRecordId, Long userId);
    
    /**
     * 计算匹配分数
     * @param request 需求
     * @param candidateUserId 候选用户ID
     * @return 匹配分数
     */
    Double calculateMatchScore(BuddyRequest request, Long candidateUserId);
    
    /**
     * 更新匹配偏好
     * @param userId 用户ID
     * @param preference 偏好设置
     * @return 是否成功
     */
    boolean updateMatchPreference(Long userId, MatchPreferenceDTO preference);
    
    /**
     * 获取匹配偏好
     * @param userId 用户ID
     * @return 匹配偏好
     */
    MatchPreferenceDTO getMatchPreference(Long userId);
    
    /**
     * 获取匹配历史
     * @param userId 用户ID
     * @return 匹配记录列表
     */
    List<MatchRecord> getMatchHistory(Long userId);
    
    /**
     * 每日定时匹配任务
     */
    void dailyMatchTask();
    
    /**
     * 完成活动
     * @param matchRecordId 匹配记录ID
     * @return 是否成功
     */
    boolean completeActivity(Long matchRecordId);
}
