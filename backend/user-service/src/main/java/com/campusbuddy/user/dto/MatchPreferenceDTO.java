package com.campusbuddy.user.dto;

import lombok.Data;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;

/**
 * 匹配偏好设置DTO
 */
@Data
public class MatchPreferenceDTO {
    
    /**
     * 性别偏好
     * ANY - 不限, MALE - 男性, FEMALE - 女性
     */
    private String genderPreference = "ANY";
    
    /**
     * 年级偏好
     * ANY - 不限, SAME - 同年级, SENIOR - 学长学姐, JUNIOR - 学弟学妹
     */
    private String gradePreference = "ANY";
    
    /**
     * 学院偏好
     * ANY - 不限, SAME - 同学院, DIFFERENT - 不同学院
     */
    private String collegePreference = "ANY";
    
    /**
     * 地理位置半径（公里）
     */
    @DecimalMin(value = "0.1", message = "位置半径不能小于0.1公里")
    @DecimalMax(value = "50.0", message = "位置半径不能大于50公里")
    private Double locationRadius = 5.0;
    
    /**
     * 最小匹配分数
     */
    @DecimalMin(value = "0.0", message = "最小匹配分数不能小于0")
    @DecimalMax(value = "1.0", message = "最小匹配分数不能大于1")
    private Double minMatchScore = 0.6;
    
    /**
     * 最大匹配数量
     */
    @NotNull(message = "最大匹配数量不能为空")
    private Integer maxMatchCount = 10;
    
    /**
     * 是否接受自动匹配
     */
    private Boolean autoMatchEnabled = true;
    
    /**
     * 是否接受跨年级匹配
     */
    private Boolean crossGradeEnabled = true;
    
    /**
     * 是否接受跨学院匹配
     */
    private Boolean crossCollegeEnabled = true;
    
    /**
     * 活动时间偏好
     * MORNING - 上午, AFTERNOON - 下午, EVENING - 晚上, WEEKEND - 周末, ANYTIME - 任何时间
     */
    private String timePreference = "ANYTIME";
    
    /**
     * 兴趣标签权重
     */
    @DecimalMin(value = "0.0", message = "兴趣标签权重不能小于0")
    @DecimalMax(value = "1.0", message = "兴趣标签权重不能大于1")
    private Double interestWeight = 0.25;
    
    /**
     * 地理位置权重
     */
    @DecimalMin(value = "0.0", message = "地理位置权重不能小于0")
    @DecimalMax(value = "1.0", message = "地理位置权重不能大于1")
    private Double locationWeight = 0.3;
    
    /**
     * 时间匹配权重
     */
    @DecimalMin(value = "0.0", message = "时间匹配权重不能小于0")
    @DecimalMax(value = "1.0", message = "时间匹配权重不能大于1")
    private Double timeWeight = 0.25;
    
    /**
     * 年级匹配权重
     */
    @DecimalMin(value = "0.0", message = "年级匹配权重不能小于0")
    @DecimalMax(value = "1.0", message = "年级匹配权重不能大于1")
    private Double gradeWeight = 0.2;
    
    /**
     * 是否只匹配已认证用户
     */
    private Boolean verifiedUsersOnly = false;
    
    /**
     * 最小信用评分要求
     */
    @DecimalMin(value = "0.0", message = "最小信用评分不能小于0")
    @DecimalMax(value = "5.0", message = "最小信用评分不能大于5")
    private Double minCreditScore = 3.0;
    
    /**
     * 黑名单过滤
     */
    private Boolean blacklistFilterEnabled = true;
    
    /**
     * 匹配通知设置
     */
    private Boolean matchNotificationEnabled = true;
    
    /**
     * 消息通知设置
     */
    private Boolean messageNotificationEnabled = true;
    
    /**
     * 评价提醒设置
     */
    private Boolean evaluationReminderEnabled = true;
}
