package com.campusbuddy.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 人脸认证DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Schema(description = "人脸认证请求")
public class FaceVerificationDTO {

    @NotBlank(message = "人脸图像数据不能为空")
    @Schema(description = "人脸图像Base64数据")
    private String faceImageBase64;

    @Schema(description = "操作类型", example = "match_request")
    private String operationType;

    @Schema(description = "设备信息")
    private String deviceInfo;

    @Schema(description = "位置信息")
    private String location;
}
