package com.campusbuddy.user.service.impl;

import com.campusbuddy.user.dto.FaceVerificationDTO;
import com.campusbuddy.user.dto.StudentVerificationDTO;
import com.campusbuddy.user.entity.User;
import com.campusbuddy.user.entity.UserVerification;
import com.campusbuddy.user.mapper.UserMapper;
import com.campusbuddy.user.mapper.UserVerificationMapper;
import com.campusbuddy.user.service.VerificationService;
import com.campusbuddy.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VerificationServiceImpl implements VerificationService {

    private final UserVerificationMapper verificationMapper;
    private final UserMapper userMapper;
    private final RestTemplate restTemplate;

    @Value("${campus.api.base-url:https://api.university.edu.cn}")
    private String campusApiBaseUrl;

    @Value("${campus.api.app-key:your-app-key}")
    private String campusApiKey;

    @Value("${face.recognition.api-url:https://api.face-recognition.com}")
    private String faceApiUrl;

    @Value("${face.recognition.api-key:your-face-api-key}")
    private String faceApiKey;

    @Override
    public Result<Map<String, Object>> verifyStudentId(Long userId, StudentVerificationDTO dto) {
        try {
            log.info("开始学号认证，用户ID: {}, 学号: {}", userId, dto.getStudentId());

            // 检查是否已经认证过
            UserVerification existingVerification = verificationMapper.findByUserIdAndType(userId, "STUDENT_ID");
            if (existingVerification != null && "SUCCESS".equals(existingVerification.getStatus())) {
                return Result.error("您已经完成学号认证");
            }

            // 调用学校统一身份认证API
            Map<String, Object> apiResult = callCampusApi(dto.getStudentId(), dto.getCampusPassword());
            
            // 保存认证记录
            UserVerification verification = new UserVerification();
            verification.setUserId(userId);
            verification.setVerificationType("STUDENT_ID");
            verification.setVerificationData(dto.getStudentId());
            verification.setCreatedAt(LocalDateTime.now());

            Map<String, Object> result = new HashMap<>();

            if ((Boolean) apiResult.get("success")) {
                // 认证成功
                verification.setStatus("SUCCESS");
                verification.setApiResponse(apiResult.toString());
                
                // 更新用户信息
                User user = userMapper.selectById(userId);
                if (user != null) {
                    user.setStudentId(dto.getStudentId());
                    user.setRealName((String) apiResult.get("realName"));
                    user.setCollege((String) apiResult.get("college"));
                    user.setMajor((String) apiResult.get("major"));
                    user.setGrade((String) apiResult.get("grade"));
                    user.setIsVerified(true);
                    userMapper.updateById(user);
                }

                result.put("success", true);
                result.put("message", "学号认证成功");
                result.put("userInfo", apiResult);
                
                log.info("学号认证成功，用户ID: {}", userId);
            } else {
                // 认证失败
                verification.setStatus("FAILED");
                verification.setApiResponse(apiResult.toString());
                
                result.put("success", false);
                result.put("message", (String) apiResult.get("message"));
                
                log.warn("学号认证失败，用户ID: {}, 原因: {}", userId, apiResult.get("message"));
            }

            verificationMapper.insert(verification);
            return Result.success(result);

        } catch (Exception e) {
            log.error("学号认证异常，用户ID: {}", userId, e);
            
            // 保存异常记录
            UserVerification verification = new UserVerification();
            verification.setUserId(userId);
            verification.setVerificationType("STUDENT_ID");
            verification.setVerificationData(dto.getStudentId());
            verification.setStatus("ERROR");
            verification.setApiResponse(e.getMessage());
            verification.setCreatedAt(LocalDateTime.now());
            verificationMapper.insert(verification);

            return Result.error("认证服务暂时不可用，请稍后重试");
        }
    }

    @Override
    public Result<Map<String, Object>> verifyFace(Long userId, FaceVerificationDTO dto) {
        try {
            log.info("开始人脸认证，用户ID: {}", userId);

            // 检查用户是否已完成学号认证
            User user = userMapper.selectById(userId);
            if (user == null || !user.getIsVerified()) {
                return Result.error("请先完成学号认证");
            }

            // 调用人脸识别API
            Map<String, Object> faceResult = callFaceRecognitionApi(dto.getFaceImageBase64(), user.getStudentId());
            
            // 保存认证记录
            UserVerification verification = new UserVerification();
            verification.setUserId(userId);
            verification.setVerificationType("FACE");
            verification.setVerificationData("face_image_" + System.currentTimeMillis());
            verification.setCreatedAt(LocalDateTime.now());

            Map<String, Object> result = new HashMap<>();

            if ((Boolean) faceResult.get("success")) {
                Double confidence = (Double) faceResult.get("confidence");
                
                if (confidence >= 0.8) { // 80%以上相似度认为认证成功
                    verification.setStatus("SUCCESS");
                    verification.setApiResponse(faceResult.toString());
                    
                    result.put("success", true);
                    result.put("message", "人脸认证成功");
                    result.put("score", Math.round(confidence * 100));
                    
                    log.info("人脸认证成功，用户ID: {}, 相似度: {}", userId, confidence);
                } else {
                    verification.setStatus("FAILED");
                    verification.setApiResponse(faceResult.toString());
                    
                    result.put("success", false);
                    result.put("message", "人脸比对相似度不足，请重新拍摄");
                    result.put("score", Math.round(confidence * 100));
                    
                    log.warn("人脸认证失败，用户ID: {}, 相似度: {}", userId, confidence);
                }
            } else {
                verification.setStatus("FAILED");
                verification.setApiResponse(faceResult.toString());
                
                result.put("success", false);
                result.put("message", (String) faceResult.get("message"));
                
                log.warn("人脸认证失败，用户ID: {}, 原因: {}", userId, faceResult.get("message"));
            }

            verificationMapper.insert(verification);
            return Result.success(result);

        } catch (Exception e) {
            log.error("人脸认证异常，用户ID: {}", userId, e);
            
            // 保存异常记录
            UserVerification verification = new UserVerification();
            verification.setUserId(userId);
            verification.setVerificationType("FACE");
            verification.setStatus("ERROR");
            verification.setApiResponse(e.getMessage());
            verification.setCreatedAt(LocalDateTime.now());
            verificationMapper.insert(verification);

            return Result.error("人脸认证服务暂时不可用，请稍后重试");
        }
    }

    @Override
    public Result<Map<String, Object>> getVerificationStatus(Long userId, String verificationType) {
        try {
            UserVerification verification = verificationMapper.findByUserIdAndType(userId, verificationType);
            
            Map<String, Object> result = new HashMap<>();
            if (verification != null) {
                result.put("verified", "SUCCESS".equals(verification.getStatus()));
                result.put("status", verification.getStatus());
                result.put("verifiedAt", verification.getCreatedAt());
            } else {
                result.put("verified", false);
                result.put("status", "NOT_VERIFIED");
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取认证状态异常，用户ID: {}, 认证类型: {}", userId, verificationType, e);
            return Result.error("获取认证状态失败");
        }
    }

    /**
     * 调用学校统一身份认证API
     */
    private Map<String, Object> callCampusApi(String studentId, String password) {
        try {
            String url = campusApiBaseUrl + "/api/auth/verify";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-API-Key", campusApiKey);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("studentId", studentId);
            requestBody.put("password", password);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            } else {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "学校认证服务响应异常");
                return errorResult;
            }
        } catch (Exception e) {
            log.error("调用学校API异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "无法连接到学校认证服务");
            return errorResult;
        }
    }

    /**
     * 调用人脸识别API
     */
    private Map<String, Object> callFaceRecognitionApi(String faceImageBase64, String studentId) {
        try {
            String url = faceApiUrl + "/api/face/verify";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + faceApiKey);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("image", faceImageBase64);
            requestBody.put("userId", studentId);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            } else {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "人脸识别服务响应异常");
                return errorResult;
            }
        } catch (Exception e) {
            log.error("调用人脸识别API异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "无法连接到人脸识别服务");
            return errorResult;
        }
    }
}
