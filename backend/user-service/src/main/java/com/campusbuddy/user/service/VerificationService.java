package com.campusbuddy.user.service;

import com.campusbuddy.user.dto.FaceVerificationDTO;
import com.campusbuddy.user.dto.StudentVerificationDTO;
import com.campusbuddy.user.entity.UserVerification;

/**
 * 认证服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface VerificationService {

    /**
     * 学号认证
     * 
     * @param userId 用户ID
     * @param dto 学号认证信息
     * @return 认证结果
     */
    UserVerification verifyStudentId(Long userId, StudentVerificationDTO dto);

    /**
     * 人脸认证
     * 
     * @param userId 用户ID
     * @param dto 人脸认证信息
     * @return 认证结果
     */
    UserVerification verifyFace(Long userId, FaceVerificationDTO dto);

    /**
     * 检查用户认证状态
     * 
     * @param userId 用户ID
     * @param verificationType 认证类型
     * @return 是否已认证
     */
    boolean isVerified(Long userId, Integer verificationType);

    /**
     * 获取用户最新认证记录
     * 
     * @param userId 用户ID
     * @param verificationType 认证类型
     * @return 认证记录
     */
    UserVerification getLatestVerification(Long userId, Integer verificationType);

    /**
     * 调用学校统一身份认证API
     * 
     * @param studentId 学号
     * @param password 密码
     * @return API响应结果
     */
    String callSchoolAuthApi(String studentId, String password);

    /**
     * 调用学校人脸比对API
     * 
     * @param studentId 学号
     * @param faceImageBase64 人脸图像
     * @return 比对结果
     */
    Double callSchoolFaceCompareApi(String studentId, String faceImageBase64);
}
