package com.campusbuddy.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户认证记录实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_verifications")
@Schema(description = "用户认证记录")
public class UserVerification {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "认证记录ID")
    private Long id;

    @TableField("user_id")
    @Schema(description = "用户ID")
    private Long userId;

    @TableField("student_id")
    @Schema(description = "学号")
    private String studentId;

    @TableField("verification_type")
    @Schema(description = "认证类型：1-学号认证，2-人脸认证")
    private Integer verificationType;

    @TableField("verification_status")
    @Schema(description = "认证状态：0-待认证，1-认证成功，2-认证失败")
    private Integer verificationStatus;

    @TableField("school_api_response")
    @Schema(description = "学校API响应数据")
    private String schoolApiResponse;

    @TableField("face_image_url")
    @Schema(description = "人脸图像URL")
    private String faceImageUrl;

    @TableField("face_compare_score")
    @Schema(description = "人脸比对分数")
    private Double faceCompareScore;

    @TableField("verification_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "认证时间")
    private LocalDateTime verificationTime;

    @TableField("expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "认证过期时间")
    private LocalDateTime expireTime;

    @TableField("ip_address")
    @Schema(description = "认证IP地址")
    private String ipAddress;

    @TableField("device_info")
    @Schema(description = "设备信息")
    private String deviceInfo;

    @TableField("failure_reason")
    @Schema(description = "认证失败原因")
    private String failureReason;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;
}
