package com.campusbuddy.user.controller;

import com.campusbuddy.user.dto.MatchResultDTO;
import com.campusbuddy.user.dto.MatchPreferenceDTO;
import com.campusbuddy.user.entity.MatchRecord;
import com.campusbuddy.user.service.MatchingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 匹配控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/match")
@RequiredArgsConstructor
@Tag(name = "匹配管理", description = "匹配相关接口")
public class MatchController {
    
    private final MatchingService matchingService;
    
    @PostMapping("/auto/{requestId}")
    @Operation(summary = "执行自动匹配", description = "为指定需求执行自动匹配")
    public ResponseEntity<List<MatchResultDTO>> performAutoMatch(
            @Parameter(description = "需求ID") @PathVariable Long requestId) {
        try {
            List<MatchResultDTO> results = matchingService.performAutoMatch(requestId);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("自动匹配失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PostMapping("/manual/{requestId}")
    @Operation(summary = "执行手动匹配", description = "用户手动触发匹配")
    public ResponseEntity<List<MatchResultDTO>> performManualMatch(
            @Parameter(description = "需求ID") @PathVariable Long requestId,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            List<MatchResultDTO> results = matchingService.performManualMatch(userId, requestId);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("手动匹配失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    @GetMapping("/results")
    @Operation(summary = "获取匹配结果", description = "获取用户的匹配结果列表")
    public ResponseEntity<List<MatchResultDTO>> getMatchResults(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            List<MatchResultDTO> results = matchingService.getUserMatchResults(userId);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("获取匹配结果失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PostMapping("/accept/{matchRecordId}")
    @Operation(summary = "接受匹配", description = "用户接受匹配结果")
    public ResponseEntity<Map<String, Object>> acceptMatch(
            @Parameter(description = "匹配记录ID") @PathVariable Long matchRecordId,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            boolean success = matchingService.acceptMatch(matchRecordId, userId);
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "匹配接受成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "匹配接受失败"
                ));
            }
        } catch (Exception e) {
            log.error("接受匹配失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "操作失败: " + e.getMessage()
            ));
        }
    }
    
    @PostMapping("/reject/{matchRecordId}")
    @Operation(summary = "拒绝匹配", description = "用户拒绝匹配结果")
    public ResponseEntity<Map<String, Object>> rejectMatch(
            @Parameter(description = "匹配记录ID") @PathVariable Long matchRecordId,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            boolean success = matchingService.rejectMatch(matchRecordId, userId);
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "匹配拒绝成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "匹配拒绝失败"
                ));
            }
        } catch (Exception e) {
            log.error("拒绝匹配失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "操作失败: " + e.getMessage()
            ));
        }
    }
    
    @PostMapping("/complete/{matchRecordId}")
    @Operation(summary = "完成活动", description = "标记活动为已完成")
    public ResponseEntity<Map<String, Object>> completeActivity(
            @Parameter(description = "匹配记录ID") @PathVariable Long matchRecordId) {
        try {
            boolean success = matchingService.completeActivity(matchRecordId);
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "活动完成标记成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "活动完成标记失败"
                ));
            }
        } catch (Exception e) {
            log.error("完成活动失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "操作失败: " + e.getMessage()
            ));
        }
    }
    
    @GetMapping("/history")
    @Operation(summary = "获取匹配历史", description = "获取用户的匹配历史记录")
    public ResponseEntity<List<MatchRecord>> getMatchHistory(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            List<MatchRecord> history = matchingService.getMatchHistory(userId);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            log.error("获取匹配历史失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PostMapping("/preference")
    @Operation(summary = "更新匹配偏好", description = "更新用户的匹配偏好设置")
    public ResponseEntity<Map<String, Object>> updateMatchPreference(
            @Valid @RequestBody MatchPreferenceDTO preference,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            boolean success = matchingService.updateMatchPreference(userId, preference);
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "匹配偏好更新成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "匹配偏好更新失败"
                ));
            }
        } catch (Exception e) {
            log.error("更新匹配偏好失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "操作失败: " + e.getMessage()
            ));
        }
    }
    
    @GetMapping("/preference")
    @Operation(summary = "获取匹配偏好", description = "获取用户的匹配偏好设置")
    public ResponseEntity<MatchPreferenceDTO> getMatchPreference(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            MatchPreferenceDTO preference = matchingService.getMatchPreference(userId);
            return ResponseEntity.ok(preference);
        } catch (Exception e) {
            log.error("获取匹配偏好失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PostMapping("/calculate-score")
    @Operation(summary = "计算匹配分数", description = "计算指定需求和用户的匹配分数")
    public ResponseEntity<Map<String, Object>> calculateMatchScore(
            @RequestParam Long requestId,
            @RequestParam Long candidateUserId) {
        try {
            // 这里需要先获取需求信息
            // BuddyRequest request = buddyRequestService.getById(requestId);
            // Double score = matchingService.calculateMatchScore(request, candidateUserId);
            
            // 临时返回示例数据
            Double score = 0.85;
            
            return ResponseEntity.ok(Map.of(
                "requestId", requestId,
                "candidateUserId", candidateUserId,
                "matchScore", score,
                "matchLevel", score >= 0.8 ? "高匹配" : score >= 0.6 ? "中匹配" : "低匹配"
            ));
        } catch (Exception e) {
            log.error("计算匹配分数失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "计算失败: " + e.getMessage()
            ));
        }
    }
    
    @GetMapping("/statistics")
    @Operation(summary = "获取匹配统计", description = "获取用户的匹配统计信息")
    public ResponseEntity<Map<String, Object>> getMatchStatistics(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            List<MatchRecord> history = matchingService.getMatchHistory(userId);
            
            long totalMatches = history.size();
            long acceptedMatches = history.stream()
                    .filter(record -> "ACCEPTED".equals(record.getStatus()))
                    .count();
            long completedMatches = history.stream()
                    .filter(record -> "COMPLETED".equals(record.getStatus()))
                    .count();
            
            double successRate = totalMatches > 0 ? (double) completedMatches / totalMatches : 0.0;
            
            return ResponseEntity.ok(Map.of(
                "totalMatches", totalMatches,
                "acceptedMatches", acceptedMatches,
                "completedMatches", completedMatches,
                "successRate", Math.round(successRate * 100.0) / 100.0,
                "pendingMatches", history.stream()
                        .filter(record -> "PENDING".equals(record.getStatus()))
                        .count()
            ));
        } catch (Exception e) {
            log.error("获取匹配统计失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
}
