package com.campusbuddy.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.campusbuddy.user.dto.UserLoginDTO;
import com.campusbuddy.user.dto.UserRegisterDTO;
import com.campusbuddy.user.entity.User;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface UserService extends IService<User> {

    /**
     * 用户注册
     * 
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    User register(UserRegisterDTO registerDTO);

    /**
     * 用户登录
     * 
     * @param loginDTO 登录信息
     * @return JWT Token
     */
    String login(UserLoginDTO loginDTO);

    /**
     * 根据学号查询用户
     * 
     * @param studentId 学号
     * @return 用户信息
     */
    User findByStudentId(String studentId);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    User findByEmail(String email);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    User findByPhone(String phone);

    /**
     * 更新用户信息
     * 
     * @param user 用户信息
     * @return 更新结果
     */
    boolean updateUser(User user);

    /**
     * 验证用户身份
     * 
     * @param userId 用户ID
     * @return 验证结果
     */
    boolean verifyUser(Long userId);

    /**
     * 更新用户信用分数
     * 
     * @param userId 用户ID
     * @param score 分数变化
     * @return 更新结果
     */
    boolean updateCreditScore(Long userId, Integer score);
}
