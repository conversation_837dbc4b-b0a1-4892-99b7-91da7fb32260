package com.campusbuddy.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户评价实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_evaluations")
@Schema(description = "用户评价")
public class UserEvaluation {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "评价ID")
    private Long id;

    @TableField("match_record_id")
    @Schema(description = "匹配记录ID")
    private Long matchRecordId;

    @TableField("evaluator_id")
    @Schema(description = "评价者ID")
    private Long evaluatorId;

    @TableField("evaluated_user_id")
    @Schema(description = "被评价用户ID")
    private Long evaluatedUserId;

    @TableField("evaluation_tags")
    @Schema(description = "评价标签ID列表（JSON格式）")
    private String evaluationTags;

    @TableField("evaluation_text")
    @Schema(description = "文字评价")
    private String evaluationText;

    @TableField("overall_rating")
    @Schema(description = "总体评分（1-5分）")
    private Integer overallRating;

    @TableField("punctuality_rating")
    @Schema(description = "准时性评分（1-5分）")
    private Integer punctualityRating;

    @TableField("communication_rating")
    @Schema(description = "沟通能力评分（1-5分）")
    private Integer communicationRating;

    @TableField("cooperation_rating")
    @Schema(description = "合作能力评分（1-5分）")
    private Integer cooperationRating;

    @TableField("is_anonymous")
    @Schema(description = "是否匿名评价")
    private Boolean isAnonymous;

    @TableField("evaluation_status")
    @Schema(description = "评价状态：0-待评价，1-已评价，2-已过期")
    private Integer evaluationStatus;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;
}
