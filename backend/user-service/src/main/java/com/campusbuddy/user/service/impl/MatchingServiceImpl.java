package com.campusbuddy.user.service.impl;

import com.campusbuddy.user.dto.MatchResultDTO;
import com.campusbuddy.user.dto.MatchPreferenceDTO;
import com.campusbuddy.user.entity.*;
import com.campusbuddy.user.mapper.*;
import com.campusbuddy.user.service.MatchingService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 匹配服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatchingServiceImpl implements MatchingService {
    
    private final BuddyRequestMapper buddyRequestMapper;
    private final UserMapper userMapper;
    private final MatchRecordMapper matchRecordMapper;
    private final MatchRuleMapper matchRuleMapper;
    private final UserTagMapper userTagMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 匹配算法权重配置
    private static final double LOCATION_WEIGHT = 0.3;
    private static final double TIME_WEIGHT = 0.25;
    private static final double INTEREST_WEIGHT = 0.25;
    private static final double GRADE_WEIGHT = 0.2;
    private static final double MIN_MATCH_SCORE = 0.6;
    
    @Override
    public List<MatchResultDTO> performAutoMatch(Long requestId) {
        log.info("开始执行自动匹配，需求ID: {}", requestId);
        
        // 获取需求信息
        BuddyRequest request = buddyRequestMapper.selectById(requestId);
        if (request == null || !"ACTIVE".equals(request.getStatus())) {
            log.warn("需求不存在或状态不正确: {}", requestId);
            return Collections.emptyList();
        }
        
        // 获取候选用户
        List<User> candidateUsers = getCandidateUsers(request);
        
        // 计算匹配分数并排序
        List<MatchResultDTO> matchResults = candidateUsers.stream()
                .map(user -> {
                    Double score = calculateMatchScore(request, user.getId());
                    if (score >= MIN_MATCH_SCORE) {
                        return createMatchResult(request, user, score);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .sorted((a, b) -> Double.compare(b.getMatchScore(), a.getMatchScore()))
                .limit(10) // 最多返回10个匹配结果
                .collect(Collectors.toList());
        
        // 保存匹配记录
        saveMatchRecords(request, matchResults);
        
        log.info("自动匹配完成，找到 {} 个匹配结果", matchResults.size());
        return matchResults;
    }
    
    @Override
    public List<MatchResultDTO> performManualMatch(Long userId, Long requestId) {
        log.info("执行手动匹配，用户ID: {}, 需求ID: {}", userId, requestId);
        
        // 检查用户权限
        BuddyRequest request = buddyRequestMapper.selectById(requestId);
        if (request == null || !request.getRequesterId().equals(userId)) {
            throw new RuntimeException("无权限执行此操作");
        }
        
        return performAutoMatch(requestId);
    }
    
    @Override
    public List<MatchResultDTO> getUserMatchResults(Long userId) {
        QueryWrapper<MatchRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("requester_id", userId)
                   .or()
                   .eq("matched_user_id", userId);
        queryWrapper.orderByDesc("created_at");
        
        List<MatchRecord> records = matchRecordMapper.selectList(queryWrapper);
        
        return records.stream()
                .map(this::convertToMatchResultDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public boolean acceptMatch(Long matchRecordId, Long userId) {
        MatchRecord record = matchRecordMapper.selectById(matchRecordId);
        if (record == null) {
            return false;
        }
        
        // 更新用户操作
        if (record.getRequesterId().equals(userId)) {
            record.setRequesterAction("ACCEPT");
        } else if (record.getMatchedUserId().equals(userId)) {
            record.setMatchedUserAction("ACCEPT");
        } else {
            return false;
        }
        
        // 检查是否双方都接受
        if ("ACCEPT".equals(record.getRequesterAction()) && 
            "ACCEPT".equals(record.getMatchedUserAction())) {
            record.setStatus("ACCEPTED");
            record.setActivityStartTime(LocalDateTime.now());
        }
        
        record.setUpdatedAt(LocalDateTime.now());
        return matchRecordMapper.updateById(record) > 0;
    }
    
    @Override
    @Transactional
    public boolean rejectMatch(Long matchRecordId, Long userId) {
        MatchRecord record = matchRecordMapper.selectById(matchRecordId);
        if (record == null) {
            return false;
        }
        
        // 更新用户操作
        if (record.getRequesterId().equals(userId)) {
            record.setRequesterAction("REJECT");
        } else if (record.getMatchedUserId().equals(userId)) {
            record.setMatchedUserAction("REJECT");
        } else {
            return false;
        }
        
        record.setStatus("REJECTED");
        record.setUpdatedAt(LocalDateTime.now());
        return matchRecordMapper.updateById(record) > 0;
    }
    
    @Override
    public Double calculateMatchScore(BuddyRequest request, Long candidateUserId) {
        User candidate = userMapper.selectById(candidateUserId);
        User requester = userMapper.selectById(request.getRequesterId());
        
        if (candidate == null || requester == null) {
            return 0.0;
        }
        
        double totalScore = 0.0;
        
        // 1. 地理位置匹配 (30%)
        double locationScore = calculateLocationScore(requester, candidate);
        totalScore += locationScore * LOCATION_WEIGHT;
        
        // 2. 时间匹配 (25%)
        double timeScore = calculateTimeScore(request, candidate);
        totalScore += timeScore * TIME_WEIGHT;
        
        // 3. 兴趣匹配 (25%)
        double interestScore = calculateInterestScore(requester.getId(), candidateUserId);
        totalScore += interestScore * INTEREST_WEIGHT;
        
        // 4. 年级匹配 (20%)
        double gradeScore = calculateGradeScore(requester, candidate);
        totalScore += gradeScore * GRADE_WEIGHT;
        
        return Math.round(totalScore * 100.0) / 100.0;
    }
    
    @Override
    public boolean updateMatchPreference(Long userId, MatchPreferenceDTO preference) {
        // 将偏好设置保存到Redis
        String key = "match_preference:" + userId;
        redisTemplate.opsForValue().set(key, preference);
        return true;
    }
    
    @Override
    public MatchPreferenceDTO getMatchPreference(Long userId) {
        String key = "match_preference:" + userId;
        Object preference = redisTemplate.opsForValue().get(key);
        
        if (preference instanceof MatchPreferenceDTO) {
            return (MatchPreferenceDTO) preference;
        }
        
        // 返回默认偏好设置
        return createDefaultPreference();
    }
    
    @Override
    public List<MatchRecord> getMatchHistory(Long userId) {
        QueryWrapper<MatchRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("requester_id", userId)
                   .or()
                   .eq("matched_user_id", userId);
        queryWrapper.orderByDesc("created_at");
        
        return matchRecordMapper.selectList(queryWrapper);
    }
    
    @Override
    @Scheduled(cron = "0 0 12 * * ?") // 每天12点执行
    public void dailyMatchTask() {
        log.info("开始执行每日定时匹配任务");
        
        // 获取所有活跃的需求
        QueryWrapper<BuddyRequest> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "ACTIVE");
        List<BuddyRequest> activeRequests = buddyRequestMapper.selectList(queryWrapper);
        
        for (BuddyRequest request : activeRequests) {
            try {
                performAutoMatch(request.getId());
            } catch (Exception e) {
                log.error("匹配需求失败: {}", request.getId(), e);
            }
        }
        
        log.info("每日定时匹配任务完成，处理了 {} 个需求", activeRequests.size());
    }
    
    @Override
    @Transactional
    public boolean completeActivity(Long matchRecordId) {
        MatchRecord record = matchRecordMapper.selectById(matchRecordId);
        if (record == null || !"ACCEPTED".equals(record.getStatus())) {
            return false;
        }
        
        record.setStatus("COMPLETED");
        record.setActivityEndTime(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        
        return matchRecordMapper.updateById(record) > 0;
    }
    
    // 私有辅助方法
    
    private List<User> getCandidateUsers(BuddyRequest request) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("id", request.getRequesterId()); // 排除发起者自己
        queryWrapper.eq("status", "ACTIVE"); // 只匹配活跃用户
        
        // 根据需求偏好过滤
        if (request.getGenderPreference() != null && !"ANY".equals(request.getGenderPreference())) {
            queryWrapper.eq("gender", request.getGenderPreference());
        }
        
        return userMapper.selectList(queryWrapper);
    }
    
    private MatchResultDTO createMatchResult(BuddyRequest request, User user, Double score) {
        MatchResultDTO result = new MatchResultDTO();
        result.setUserId(user.getId());
        result.setNickname(user.getNickname());
        result.setAvatar(user.getAvatar());
        result.setCollege(user.getCollege());
        result.setMajor(user.getMajor());
        result.setGrade(user.getGrade());
        result.setMatchScore(score);
        result.setMatchReason(generateMatchReason(request, user, score));
        return result;
    }
    
    private void saveMatchRecords(BuddyRequest request, List<MatchResultDTO> matchResults) {
        for (MatchResultDTO result : matchResults) {
            MatchRecord record = new MatchRecord();
            record.setRequestId(request.getId());
            record.setRequesterId(request.getRequesterId());
            record.setMatchedUserId(result.getUserId());
            record.setMatchScore(BigDecimal.valueOf(result.getMatchScore()));
            record.setMatchReason(result.getMatchReason());
            record.setStatus("PENDING");
            record.setCreatedAt(LocalDateTime.now());
            record.setUpdatedAt(LocalDateTime.now());
            
            matchRecordMapper.insert(record);
        }
    }
    
    private MatchResultDTO convertToMatchResultDTO(MatchRecord record) {
        // 实现转换逻辑
        MatchResultDTO dto = new MatchResultDTO();
        // ... 设置属性
        return dto;
    }
    
    private double calculateLocationScore(User requester, User candidate) {
        // 简单的校区匹配逻辑
        if (requester.getCollege() != null && requester.getCollege().equals(candidate.getCollege())) {
            return 1.0;
        }
        return 0.5;
    }
    
    private double calculateTimeScore(BuddyRequest request, User candidate) {
        // 时间匹配逻辑（简化版）
        return 0.8; // 默认返回较高分数
    }
    
    private double calculateInterestScore(Long requesterId, Long candidateId) {
        // 获取用户标签并计算相似度
        List<UserTag> requesterTags = getUserTags(requesterId);
        List<UserTag> candidateTags = getUserTags(candidateId);
        
        if (requesterTags.isEmpty() || candidateTags.isEmpty()) {
            return 0.5;
        }
        
        Set<String> requesterTagNames = requesterTags.stream()
                .map(UserTag::getTagName)
                .collect(Collectors.toSet());
        
        Set<String> candidateTagNames = candidateTags.stream()
                .map(UserTag::getTagName)
                .collect(Collectors.toSet());
        
        // 计算交集
        Set<String> intersection = new HashSet<>(requesterTagNames);
        intersection.retainAll(candidateTagNames);
        
        // 计算并集
        Set<String> union = new HashSet<>(requesterTagNames);
        union.addAll(candidateTagNames);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    private double calculateGradeScore(User requester, User candidate) {
        // 年级匹配逻辑
        if (requester.getGrade() != null && requester.getGrade().equals(candidate.getGrade())) {
            return 1.0;
        }
        return 0.7; // 不同年级也给一定分数
    }
    
    private List<UserTag> getUserTags(Long userId) {
        QueryWrapper<UserTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return userTagMapper.selectList(queryWrapper);
    }
    
    private String generateMatchReason(BuddyRequest request, User user, Double score) {
        List<String> reasons = new ArrayList<>();
        
        if (score >= 0.9) {
            reasons.add("高度匹配");
        } else if (score >= 0.8) {
            reasons.add("很好匹配");
        } else if (score >= 0.7) {
            reasons.add("较好匹配");
        } else {
            reasons.add("一般匹配");
        }
        
        // 添加具体匹配原因
        User requester = userMapper.selectById(request.getRequesterId());
        if (requester.getCollege() != null && requester.getCollege().equals(user.getCollege())) {
            reasons.add("同校区");
        }
        if (requester.getGrade() != null && requester.getGrade().equals(user.getGrade())) {
            reasons.add("同年级");
        }
        
        return String.join("、", reasons);
    }
    
    private MatchPreferenceDTO createDefaultPreference() {
        MatchPreferenceDTO preference = new MatchPreferenceDTO();
        preference.setGenderPreference("ANY");
        preference.setGradePreference("ANY");
        preference.setLocationRadius(5.0);
        preference.setMinMatchScore(0.6);
        return preference;
    }
}
