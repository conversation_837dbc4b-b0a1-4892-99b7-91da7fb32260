package com.campusbuddy.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
@Schema(description = "用户信息")
public class User {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "用户ID")
    private Long id;

    @NotBlank(message = "学号不能为空")
    @Pattern(regexp = "^[0-9]{8,12}$", message = "学号格式不正确")
    @TableField("student_id")
    @Schema(description = "学号")
    private String studentId;

    @NotBlank(message = "用户名不能为空")
    @TableField("username")
    @Schema(description = "用户名")
    private String username;

    @NotBlank(message = "真实姓名不能为空")
    @TableField("real_name")
    @Schema(description = "真实姓名")
    private String realName;

    @Email(message = "邮箱格式不正确")
    @TableField("email")
    @Schema(description = "邮箱")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @TableField("phone")
    @Schema(description = "手机号")
    private String phone;

    @TableField("password")
    @Schema(description = "密码")
    private String password;

    @TableField("avatar")
    @Schema(description = "头像URL")
    private String avatar;

    @TableField("gender")
    @Schema(description = "性别：0-未知，1-男，2-女")
    private Integer gender;

    @TableField("college")
    @Schema(description = "学院")
    private String college;

    @TableField("major")
    @Schema(description = "专业")
    private String major;

    @TableField("grade")
    @Schema(description = "年级")
    private String grade;

    @TableField("bio")
    @Schema(description = "个人简介")
    private String bio;

    @TableField("interests")
    @Schema(description = "兴趣爱好（JSON格式）")
    private String interests;

    @TableField("location")
    @Schema(description = "常用位置（JSON格式）")
    private String location;

    @TableField("credit_score")
    @Schema(description = "信用分数")
    private Integer creditScore;

    @TableField("status")
    @Schema(description = "账户状态：0-正常，1-冻结，2-注销")
    private Integer status;

    @TableField("is_verified")
    @Schema(description = "是否已认证")
    private Boolean isVerified;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;
}
