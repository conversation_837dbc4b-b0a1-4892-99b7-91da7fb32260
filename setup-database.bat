@echo off

echo ========================================
echo   Database Setup for Campus Buddy
echo ========================================
echo.

echo [INFO] This script will help you set up the MySQL database
echo.

:: Check if MySQL is available
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] MySQL not found in PATH
    echo Please make sure MySQL is installed and added to system PATH
    echo.
    echo Installation options:
    echo 1. Download MySQL from: https://dev.mysql.com/downloads/mysql/
    echo 2. Or use XAMPP: https://www.apachefriends.org/
    echo 3. Or use WAMP: https://www.wampserver.com/
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] MySQL found
echo.

:: Get database connection info
set /p db_host="Enter MySQL host (default: localhost): "
if "%db_host%"=="" set db_host=localhost

set /p db_port="Enter MySQL port (default: 3306): "
if "%db_port%"=="" set db_port=3306

set /p db_root_password="Enter MySQL root password: "
if "%db_root_password%"=="" (
    echo [ERROR] Root password is required
    pause
    exit /b 1
)

echo.
echo [INFO] Testing MySQL connection...

:: Test connection
mysql -h %db_host% -P %db_port% -u root -p%db_root_password% -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Cannot connect to MySQL
    echo Please check your host, port, and password
    pause
    exit /b 1
)

echo [SUCCESS] MySQL connection successful
echo.

:: Create database
echo [INFO] Creating database 'campus_buddy'...
mysql -h %db_host% -P %db_port% -u root -p%db_root_password% -e "CREATE DATABASE IF NOT EXISTS campus_buddy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if %errorlevel% neq 0 (
    echo [ERROR] Failed to create database
    pause
    exit /b 1
)

echo [SUCCESS] Database 'campus_buddy' created
echo.

:: Create user (optional)
set /p create_user="Create dedicated user 'campus_buddy'? (y/n): "
if /i "%create_user%"=="y" (
    echo [INFO] Creating user 'campus_buddy'...
    mysql -h %db_host% -P %db_port% -u root -p%db_root_password% -e "CREATE USER IF NOT EXISTS 'campus_buddy'@'localhost' IDENTIFIED BY 'campus_buddy123';"
    mysql -h %db_host% -P %db_port% -u root -p%db_root_password% -e "GRANT ALL PRIVILEGES ON campus_buddy.* TO 'campus_buddy'@'localhost';"
    mysql -h %db_host% -P %db_port% -u root -p%db_root_password% -e "FLUSH PRIVILEGES;"
    echo [SUCCESS] User 'campus_buddy' created with password 'campus_buddy123'
)

echo.

:: Import database schema
if exist "database\init.sql" (
    echo [INFO] Importing database schema...
    mysql -h %db_host% -P %db_port% -u root -p%db_root_password% campus_buddy < database\init.sql
    
    if %errorlevel% neq 0 (
        echo [WARNING] Some errors occurred during schema import
        echo This might be normal for first-time setup
    ) else (
        echo [SUCCESS] Database schema imported successfully
    )
) else (
    echo [WARNING] Database schema file 'database\init.sql' not found
    echo You may need to create tables manually or check the file path
)

echo.

:: Show final configuration
echo ========================================
echo   Database Setup Complete!
echo ========================================
echo.
echo Database Configuration:
echo   Host: %db_host%
echo   Port: %db_port%
echo   Database: campus_buddy
echo   Root User: root
echo   Root Password: %db_root_password%
if /i "%create_user%"=="y" (
    echo   App User: campus_buddy
    echo   App Password: campus_buddy123
)
echo.
echo Next Steps:
echo 1. Update application.yml files with your database settings
echo 2. Run 'start-local.bat' to start the application
echo 3. Or manually start services as described in LOCAL_SETUP_GUIDE.md
echo.

pause
