@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   校园搭子系统 - 停止脚本
echo ========================================
echo.

:: 检查Docker是否运行
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未运行或无法访问
    pause
    exit /b 1
)

:: 显示当前运行的容器
echo 📦 当前运行的容器:
docker-compose ps

echo.
set /p choice="选择停止方式 (1-仅停止/2-停止并删除数据/3-取消): "

if "%choice%"=="1" (
    echo.
    echo 🛑 停止所有服务...
    docker-compose down
    if %errorlevel% equ 0 (
        echo ✅ 所有服务已停止
    ) else (
        echo ❌ 停止过程中出现错误
    )
) else if "%choice%"=="2" (
    echo.
    echo ⚠️  警告: 这将删除所有数据库数据！
    set /p confirm="确认删除所有数据? (yes/no): "
    if /i "%confirm%"=="yes" (
        echo.
        echo 🗑️  停止服务并删除数据...
        docker-compose down -v
        if %errorlevel% equ 0 (
            echo ✅ 所有服务已停止，数据已删除
        ) else (
            echo ❌ 停止过程中出现错误
        )
    ) else (
        echo 操作已取消
    )
) else if "%choice%"=="3" (
    echo 操作已取消
) else (
    echo 无效选择
)

echo.
echo 🔧 其他管理命令:
echo    查看状态:      docker-compose ps
echo    查看日志:      docker-compose logs
echo    重启系统:      docker-compose restart
echo    清理镜像:      docker system prune
echo.
pause
