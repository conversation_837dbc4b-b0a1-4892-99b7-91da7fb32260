@echo off

echo ========================================
echo   Campus Buddy System - Simple Start
echo ========================================
echo.

:: Check if Docker is running
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker not found or not running.
    echo Please make sure Docker Desktop is installed and running.
    pause
    exit /b 1
)

echo Docker found. Starting system...
echo.

:: Stop any existing containers
echo Stopping existing containers...
docker-compose down

:: Start all services
echo Starting all services...
docker-compose up -d --build

if %errorlevel% neq 0 (
    echo ERROR: Failed to start services.
    echo Check the error messages above.
    pause
    exit /b 1
)

echo.
echo System is starting up...
echo Please wait a few minutes for all services to initialize.
echo.
echo Access URLs:
echo   Main App:       http://localhost
echo   API Gateway:    http://localhost:8080
echo   Eureka Console: http://localhost:8761
echo.
echo To check status: docker-compose ps
echo To view logs:    docker-compose logs -f
echo To stop system:  docker-compose down
echo.

pause
