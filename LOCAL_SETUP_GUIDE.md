# 🏠 本地环境搭建指南 (无需Docker)

## 📋 环境要求

### 必需软件
- **Node.js 18+** - 前端开发环境
- **Java JDK 17+** - 后端运行环境  
- **Maven 3.8+** - 项目构建工具
- **MySQL 8.0+** - 数据库
- **Redis 7.x+** - 缓存数据库 (可选)

### 推荐IDE
- **IntelliJ IDEA** - Java开发
- **VS Code** - 前端开发

## 🔧 软件安装

### 1. 安装Node.js
```bash
# 下载并安装Node.js 18+
# 官网: https://nodejs.org/
# 验证安装
node --version
npm --version
```

### 2. 安装Java JDK 17
```bash
# 下载并安装JDK 17
# 推荐: https://adoptium.net/
# 验证安装
java -version
javac -version
```

### 3. 安装Maven
```bash
# 下载并安装Maven 3.8+
# 官网: https://maven.apache.org/download.cgi
# 配置环境变量后验证
mvn --version
```

### 4. 安装MySQL 8.0
```bash
# 下载并安装MySQL 8.0
# 官网: https://dev.mysql.com/downloads/mysql/
# 或使用MySQL Installer for Windows
```

### 5. 安装Redis (可选)
```bash
# Windows用户可以使用:
# 1. Redis for Windows: https://github.com/microsoftarchive/redis/releases
# 2. 或者使用WSL安装Linux版本
```

## 🗄️ 数据库配置

### MySQL配置
1. **启动MySQL服务**
2. **创建数据库和用户**
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE IF NOT EXISTS campus_buddy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户 (可选)
CREATE USER 'campus_buddy'@'localhost' IDENTIFIED BY 'campus_buddy123';
GRANT ALL PRIVILEGES ON campus_buddy.* TO 'campus_buddy'@'localhost';
FLUSH PRIVILEGES;
```

3. **导入数据库结构**
```bash
# 在项目根目录执行
mysql -u root -p123456 campus_buddy < database/init.sql
```

### Redis配置 (可选)
```bash
# 启动Redis服务
redis-server

# 或在Windows服务中启动
# 默认端口: 6379
# 无密码
```

## 🚀 启动系统

### 准备工作
1. **设置数据库**
```bash
# 运行数据库设置脚本
setup-database.bat
```

2. **或手动设置数据库**
```sql
mysql -u root -p
CREATE DATABASE IF NOT EXISTS campus_buddy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 方式一: 使用启动脚本 (推荐)
```bash
# 在项目根目录执行
start-local.bat
```

### 方式二: 手动启动

#### 1. 启动前端
```bash
cd frontend
npm install          # 首次运行
npm run dev          # 启动开发服务器
# 访问: http://localhost:5173
```

#### 2. 启动后端服务

**编译项目**
```bash
cd backend
mvn clean compile -DskipTests
```

**启动用户服务**
```bash
cd backend/user-service
mvn spring-boot:run
# 服务端口: 8081
```

**启动其他服务 (可选)**
```bash
# 在新的命令行窗口中分别启动

# Eureka注册中心
cd backend/eureka-server
mvn spring-boot:run
# 访问: http://localhost:8761

# API网关
cd backend/gateway  
mvn spring-boot:run
# 服务端口: 8080

# 匹配服务
cd backend/match-service
mvn spring-boot:run
# 服务端口: 8082

# 认证服务
cd backend/auth-service
mvn spring-boot:run
# 服务端口: 8083

# 通知服务
cd backend/notification-service
mvn spring-boot:run
# 服务端口: 8084
```

## 🔍 验证启动

### 检查服务状态
1. **前端应用**: http://localhost:5173
2. **用户服务**: http://localhost:8081/user-service/actuator/health
3. **API网关**: http://localhost:8080/actuator/health
4. **Eureka控制台**: http://localhost:8761

### 检查数据库连接
```bash
# 检查MySQL连接
mysql -u root -p123456 -e "SHOW DATABASES;"

# 检查Redis连接 (如果安装)
redis-cli ping
```

## ⚠️ 常见问题

### 端口冲突
如果遇到端口被占用的问题:
```bash
# 查看端口占用
netstat -ano | findstr :8080
netstat -ano | findstr :3306
netstat -ano | findstr :5173

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 数据库连接失败
1. 确认MySQL服务已启动
2. 检查用户名密码是否正确
3. 确认数据库`campus_buddy`已创建
4. 检查防火墙设置

### Maven依赖下载慢
配置国内镜像源 (settings.xml):
```xml
<mirror>
    <id>aliyun</id>
    <mirrorOf>central</mirrorOf>
    <name>Aliyun Maven</name>
    <url>https://maven.aliyun.com/repository/public</url>
</mirror>
```

### 前端依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 使用国内镜像
npm config set registry https://registry.npmmirror.com

# 重新安装
npm install
```

## 📝 开发建议

1. **使用IDE**: 推荐使用IntelliJ IDEA打开backend目录
2. **热重载**: 前端支持热重载，后端可使用spring-boot-devtools
3. **调试模式**: 可以在IDE中直接调试Spring Boot应用
4. **日志查看**: 各服务日志会在控制台输出

## 🎯 下一步

启动成功后，你可以:
1. 访问前端应用进行用户注册
2. 查看Eureka控制台确认服务注册
3. 使用API文档测试接口
4. 开始开发新功能

如有问题，请查看各服务的控制台日志进行排查。
