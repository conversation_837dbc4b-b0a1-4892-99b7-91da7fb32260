# 用户管理 API

## 用户注册

### 接口信息
- **URL**: `/api/users/register`
- **Method**: `POST`
- **认证**: 无需认证

### 请求参数
```json
{
  "studentId": "20210001",
  "username": "张三",
  "realName": "张三",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "password": "Password123",
  "confirmPassword": "Password123",
  "college": "计算机科学与技术学院",
  "major": "软件工程",
  "grade": "2021",
  "gender": 1,
  "verificationCode": "123456"
}
```

### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| studentId | string | 是 | 学号，8-12位数字 |
| username | string | 是 | 用户名 |
| realName | string | 是 | 真实姓名 |
| email | string | 否 | 邮箱地址 |
| phone | string | 否 | 手机号 |
| password | string | 是 | 密码，至少8位包含大小写字母和数字 |
| confirmPassword | string | 是 | 确认密码 |
| college | string | 否 | 学院 |
| major | string | 否 | 专业 |
| grade | string | 否 | 年级 |
| gender | integer | 否 | 性别：1-男，2-女 |
| verificationCode | string | 是 | 验证码 |

### 响应示例
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "id": 1,
    "studentId": "20210001",
    "username": "张三",
    "email": "<EMAIL>",
    "creditScore": 100,
    "isVerified": false,
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

## 用户登录

### 接口信息
- **URL**: `/api/users/login`
- **Method**: `POST`
- **认证**: 无需认证

### 请求参数
```json
{
  "studentId": "20210001",
  "password": "Password123",
  "rememberMe": true
}
```

### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| studentId | string | 是 | 学号 |
| password | string | 是 | 密码 |
| rememberMe | boolean | 否 | 是否记住登录状态 |

### 响应示例
```json
{
  "success": true,
  "message": "登录成功",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 86400,
  "user": {
    "id": 1,
    "studentId": "20210001",
    "username": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "creditScore": 100
  }
}
```

## 获取用户信息

### 接口信息
- **URL**: `/api/users/{id}`
- **Method**: `GET`
- **认证**: 需要JWT Token

### 路径参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | integer | 是 | 用户ID |

### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "studentId": "20210001",
    "username": "张三",
    "realName": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "https://example.com/avatar.jpg",
    "gender": 1,
    "college": "计算机科学与技术学院",
    "major": "软件工程",
    "grade": "2021",
    "bio": "热爱编程的大学生",
    "interests": ["编程", "篮球", "音乐"],
    "creditScore": 100,
    "isVerified": true,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

## 更新用户信息

### 接口信息
- **URL**: `/api/users/{id}`
- **Method**: `PUT`
- **认证**: 需要JWT Token

### 路径参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | integer | 是 | 用户ID |

### 请求参数
```json
{
  "username": "张三三",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "avatar": "https://example.com/new-avatar.jpg",
  "bio": "更新后的个人简介",
  "interests": ["编程", "篮球", "音乐", "阅读"]
}
```

### 响应示例
```json
{
  "success": true,
  "message": "更新成功"
}
```

## 用户认证

### 接口信息
- **URL**: `/api/users/{id}/verify`
- **Method**: `POST`
- **认证**: 需要JWT Token

### 路径参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | integer | 是 | 用户ID |

### 请求参数
```json
{
  "verificationType": "student_card",
  "verificationData": {
    "studentCardImage": "https://example.com/student-card.jpg",
    "idCardImage": "https://example.com/id-card.jpg"
  }
}
```

### 响应示例
```json
{
  "success": true,
  "message": "认证申请已提交，请等待审核"
}
```

## 修改密码

### 接口信息
- **URL**: `/api/users/password`
- **Method**: `PUT`
- **认证**: 需要JWT Token

### 请求参数
```json
{
  "oldPassword": "OldPassword123",
  "newPassword": "NewPassword123",
  "confirmPassword": "NewPassword123"
}
```

### 响应示例
```json
{
  "success": true,
  "message": "密码修改成功"
}
```

## 上传头像

### 接口信息
- **URL**: `/api/users/avatar`
- **Method**: `POST`
- **认证**: 需要JWT Token
- **Content-Type**: `multipart/form-data`

### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| avatar | file | 是 | 头像文件，支持jpg、png格式，最大2MB |

### 响应示例
```json
{
  "success": true,
  "message": "头像上传成功",
  "data": {
    "avatarUrl": "https://example.com/avatars/user_1_avatar.jpg"
  }
}
```

## 发送验证码

### 接口信息
- **URL**: `/api/users/verification-code`
- **Method**: `POST`
- **认证**: 无需认证

### 请求参数
```json
{
  "type": "email",
  "target": "<EMAIL>",
  "purpose": "register"
}
```

### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| type | string | 是 | 验证码类型：email、sms |
| target | string | 是 | 目标邮箱或手机号 |
| purpose | string | 是 | 用途：register、reset_password |

### 响应示例
```json
{
  "success": true,
  "message": "验证码已发送"
}
```
