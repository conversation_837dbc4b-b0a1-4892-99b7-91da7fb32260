# 校园搭子即时匹配系统 API 文档

## 概述

校园搭子即时匹配系统提供RESTful API接口，支持用户管理、需求发布、智能匹配、实时通信等核心功能。

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **API版本**: v1.0
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## API 模块

### 1. 用户管理 (User Management)
- [用户注册](./user.md#register)
- [用户登录](./user.md#login)
- [获取用户信息](./user.md#get-user-info)
- [更新用户信息](./user.md#update-user-info)
- [用户认证](./user.md#verify-user)

### 2. 需求管理 (Request Management)
- [发布需求](./request.md#create-request)
- [获取需求列表](./request.md#get-requests)
- [获取需求详情](./request.md#get-request-detail)
- [更新需求](./request.md#update-request)
- [取消需求](./request.md#cancel-request)

### 3. 匹配服务 (Match Service)
- [智能匹配](./match.md#smart-match)
- [确认匹配](./match.md#confirm-match)
- [拒绝匹配](./match.md#reject-match)
- [获取匹配记录](./match.md#get-matches)
- [完成匹配](./match.md#complete-match)

### 4. 消息通信 (Message Service)
- [发送消息](./message.md#send-message)
- [获取消息列表](./message.md#get-messages)
- [标记已读](./message.md#mark-read)
- [获取会话列表](./message.md#get-conversations)

### 5. 通知服务 (Notification Service)
- [获取通知列表](./notification.md#get-notifications)
- [标记通知已读](./notification.md#mark-notification-read)
- [推送设置](./notification.md#notification-settings)

### 6. 评价系统 (Rating System)
- [提交评价](./rating.md#submit-rating)
- [获取评价](./rating.md#get-ratings)
- [举报用户](./rating.md#report-user)

## 认证说明

### JWT Token 格式
```
Authorization: Bearer <token>
```

### Token 获取
通过用户登录接口获取JWT Token，Token有效期为24小时。

### Token 刷新
Token过期前可通过刷新接口获取新Token。

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| USER_NOT_FOUND | 用户不存在 |
| INVALID_PASSWORD | 密码错误 |
| TOKEN_EXPIRED | Token已过期 |
| INSUFFICIENT_PERMISSION | 权限不足 |
| REQUEST_NOT_FOUND | 需求不存在 |
| MATCH_ALREADY_EXISTS | 匹配已存在 |
| INVALID_MATCH_STATUS | 无效的匹配状态 |

## 限流说明

为保证系统稳定性，API接口实施限流策略：

- **普通接口**: 每分钟100次请求
- **登录接口**: 每分钟10次请求
- **发送消息**: 每分钟30次请求

## WebSocket 接口

### 连接地址
```
ws://localhost:8080/ws/match
```

### 消息格式
```json
{
  "type": "MATCH_FOUND",
  "data": {
    "matchId": 123,
    "requestId": 456,
    "responder": {
      "id": 789,
      "username": "张三"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 消息类型
- `MATCH_FOUND`: 找到匹配
- `MATCH_CONFIRMED`: 匹配确认
- `MATCH_REJECTED`: 匹配拒绝
- `NEW_MESSAGE`: 新消息
- `SYSTEM_NOTIFICATION`: 系统通知

## 测试环境

### 测试账号
- 学号: 20210001
- 密码: Test123456

### Swagger UI
访问地址: http://localhost:8080/swagger-ui.html

### Postman Collection
[下载 Postman Collection](./postman/campus-buddy-api.json)

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现用户管理、需求管理、匹配服务等核心功能
- 支持实时消息推送
- 完善的错误处理和限流机制
