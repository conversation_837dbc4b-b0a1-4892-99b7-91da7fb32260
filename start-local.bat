@echo off

echo ========================================
echo   Campus Buddy System - Local Start
echo   (No Docker Required)
echo ========================================
echo.

:: Check environment
echo [INFO] Checking development environment...

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found. Please install Node.js 18+
    echo Download: https://nodejs.org/
    pause
    exit /b 1
)
echo [SUCCESS] Node.js environment OK

:: Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java not found. Please install JDK 17+
    echo Download: https://adoptium.net/
    pause
    exit /b 1
)
echo [SUCCESS] Java environment OK

:: Check Maven
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Maven not found. Please install Maven 3.8+
    echo Download: https://maven.apache.org/download.cgi
    pause
    exit /b 1
)
echo [SUCCESS] Maven environment OK

echo.
echo [INFO] Environment check completed successfully!
echo.

:: Database setup instructions
echo ========================================
echo   Database Setup Required
echo ========================================
echo.
echo Before starting the system, please ensure:
echo.
echo 1. MySQL 8.0+ is installed and running
echo    - Host: localhost
echo    - Port: 3306
echo    - Root password: 123456
echo    - Database: campus_buddy
echo.
echo 2. Redis is installed and running (optional)
echo    - Host: localhost
echo    - Port: 6379
echo    - No password required
echo.
echo 3. Import database schema:
echo    mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS campus_buddy;"
echo    mysql -u root -p123456 campus_buddy ^< database/init.sql
echo.

set /p db_ready="Have you set up MySQL database? (y/n): "
if /i "%db_ready%" neq "y" (
    echo.
    echo Please set up the database first and run this script again.
    echo See STARTUP_GUIDE.md for detailed instructions.
    pause
    exit /b 0
)

echo.
echo [INFO] Starting Campus Buddy System...
echo.

:: Install frontend dependencies
echo [FRONTEND] Installing frontend dependencies...
cd frontend
if not exist node_modules (
    echo [INFO] First run, installing packages...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Frontend dependency installation failed
        pause
        exit /b 1
    )
)
echo [SUCCESS] Frontend dependencies installed

:: Start frontend development server
echo [FRONTEND] Starting frontend development server...
start "Frontend Server" cmd /k "npm run dev"
cd ..

echo.

:: Compile backend project
echo [BACKEND] Compiling backend project...
cd backend
mvn clean compile -DskipTests -q
if %errorlevel% neq 0 (
    echo [ERROR] Backend project compilation failed
    pause
    exit /b 1
)
echo [SUCCESS] Backend project compiled

:: Start user service
echo [BACKEND] Starting user service...
cd user-service
start "User Service" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=local"
cd ..

echo [INFO] Waiting for services to start...
timeout /t 10 /nobreak >nul

:: Start other services if they exist
if exist match-service (
    echo [BACKEND] Starting match service...
    cd match-service
    start "Match Service" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=local"
    cd ..
    timeout /t 5 /nobreak >nul
)

if exist auth-service (
    echo [BACKEND] Starting auth service...
    cd auth-service
    start "Auth Service" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=local"
    cd ..
    timeout /t 5 /nobreak >nul
)

if exist notification-service (
    echo [BACKEND] Starting notification service...
    cd notification-service
    start "Notification Service" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=local"
    cd ..
    timeout /t 5 /nobreak >nul
)

if exist gateway (
    echo [BACKEND] Starting API gateway...
    cd gateway
    start "API Gateway" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=local"
    cd ..
    timeout /t 10 /nobreak >nul
)

if exist eureka-server (
    echo [BACKEND] Starting Eureka server...
    cd eureka-server
    start "Eureka Server" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=local"
    cd ..
    timeout /t 15 /nobreak >nul
)

cd ..

echo.
echo ========================================
echo   Local Development Environment Started!
echo ========================================
echo.
echo Access URLs:
echo   Frontend App:   http://localhost:5173
echo   User Service:   http://localhost:8081
echo   API Gateway:    http://localhost:8080 (if started)
echo   Eureka Console: http://localhost:8761 (if started)
echo.
echo Notes:
echo   1. Make sure MySQL and Redis are running
echo   2. First startup may take longer
echo   3. Check for port conflicts if services fail
echo   4. Close service windows manually when done
echo.

set /p open_browser="Open browser to access the application? (y/n): "
if /i "%open_browser%" equ "y" (
    timeout /t 5 /nobreak >nul
    start http://localhost:5173
)

echo.
echo Press any key to exit...
pause >nul
