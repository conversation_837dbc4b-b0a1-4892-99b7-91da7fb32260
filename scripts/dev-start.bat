@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo   校园搭子系统 - 开发环境启动脚本
echo ========================================
echo.

:: 检查Node.js
echo [检查] 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)
echo [成功] Node.js环境正常

:: 检查Java
echo [检查] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo [错误] Java未安装，请先安装JDK 17+
    pause
    exit /b 1
)
echo [成功] Java环境正常

:: 检查Maven
echo [检查] 检查Maven环境...
mvn --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Maven未安装，请先安装Maven
    pause
    exit /b 1
)
echo [成功] Maven环境正常

:: 启动MySQL (如果使用Docker)
echo [启动] 启动MySQL数据库...
docker ps | findstr mysql >nul 2>&1
if errorlevel 1 (
    echo [提示] 启动MySQL容器...
    docker run -d --name campus-buddy-mysql -e MYSQL_ROOT_PASSWORD=123456 -e MYSQL_DATABASE=campus_buddy -e MYSQL_USER=campus_buddy -e MYSQL_PASSWORD=campus_buddy123 -p 3306:3306 mysql:8.0
    timeout /t 30 /nobreak >nul
) else (
    echo [成功] MySQL已运行
)

:: 启动Redis (如果使用Docker)
echo [启动] 启动Redis缓存...
docker ps | findstr redis >nul 2>&1
if errorlevel 1 (
    echo [提示] 启动Redis容器...
    docker run -d --name campus-buddy-redis -p 6379:6379 redis:7-alpine
    timeout /t 10 /nobreak >nul
) else (
    echo [成功] Redis已运行
)

:: 安装前端依赖
echo [安装] 安装前端依赖...
cd frontend
if not exist node_modules (
    echo [提示] 首次运行，安装依赖包...
    npm install
    if errorlevel 1 (
        echo [错误] 前端依赖安装失败
        pause
        exit /b 1
    )
)
echo [成功] 前端依赖已安装

:: 启动前端开发服务器
echo [启动] 启动前端开发服务器...
start "前端服务器" cmd /k "npm run dev"
cd ..

:: 等待前端服务器启动
timeout /t 5 /nobreak >nul

:: 编译后端项目
echo [编译] 编译后端项目...
cd backend
mvn clean compile -DskipTests -q
if errorlevel 1 (
    echo [错误] 后端项目编译失败
    pause
    exit /b 1
)
echo [成功] 后端项目编译完成

:: 启动用户服务
echo [启动] 启动用户服务...
cd user-service
start "用户服务" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"
cd ..

:: 等待服务启动
timeout /t 10 /nobreak >nul

:: 启动匹配服务 (如果存在)
if exist match-service (
    echo [启动] 启动匹配服务...
    cd match-service
    start "匹配服务" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"
    cd ..
    timeout /t 10 /nobreak >nul
)

:: 启动认证服务 (如果存在)
if exist auth-service (
    echo [启动] 启动认证服务...
    cd auth-service
    start "认证服务" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"
    cd ..
    timeout /t 10 /nobreak >nul
)

:: 启动通知服务 (如果存在)
if exist notification-service (
    echo [启动] 启动通知服务...
    cd notification-service
    start "通知服务" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"
    cd ..
    timeout /t 10 /nobreak >nul
)

:: 启动API网关 (如果存在)
if exist gateway (
    echo [启动] 启动API网关...
    cd gateway
    start "API网关" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"
    cd ..
    timeout /t 15 /nobreak >nul
)

cd ..

echo.
echo ========================================
echo   开发环境启动完成！
echo ========================================
echo.
echo 访问地址：
echo   前端应用: http://localhost:3000
echo   API文档:  http://localhost:8080/swagger-ui.html
echo   用户服务: http://localhost:8081
echo.
echo 注意事项：
echo   1. 请确保MySQL和Redis服务正常运行
echo   2. 首次启动可能需要较长时间
echo   3. 如遇到端口冲突，请检查端口占用情况
echo   4. 关闭时请手动关闭各个服务窗口
echo.
echo 按任意键退出...
pause >nul
