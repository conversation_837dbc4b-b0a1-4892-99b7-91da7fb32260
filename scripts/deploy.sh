#!/bin/bash

# 校园搭子即时匹配系统部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|test|prod
# 操作: start|stop|restart|build|logs

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="campus-buddy"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV=${1:-dev}
ACTION=${2:-start}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 设置环境变量
set_environment() {
    log_info "设置环境变量 ($ENV)..."
    
    case $ENV in
        dev)
            export SPRING_PROFILES_ACTIVE=dev
            export MYSQL_ROOT_PASSWORD=123456
            export REDIS_PASSWORD=""
            ;;
        test)
            export SPRING_PROFILES_ACTIVE=test
            export MYSQL_ROOT_PASSWORD=test123456
            export REDIS_PASSWORD="test123"
            ;;
        prod)
            export SPRING_PROFILES_ACTIVE=prod
            export MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-prod123456}
            export REDIS_PASSWORD=${REDIS_PASSWORD:-prod123}
            ;;
        *)
            log_error "不支持的环境: $ENV"
            exit 1
            ;;
    esac
    
    log_success "环境变量设置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建前端
    log_info "构建前端镜像..."
    docker-compose build frontend
    
    # 构建后端服务
    log_info "构建后端服务镜像..."
    docker-compose build user-service match-service auth-service notification-service gateway
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 首先启动基础设施服务
    log_info "启动基础设施服务..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动注册中心
    log_info "启动注册中心..."
    docker-compose up -d eureka
    
    # 等待注册中心启动
    sleep 20
    
    # 启动微服务
    log_info "启动微服务..."
    docker-compose up -d user-service match-service auth-service notification-service
    
    # 等待微服务启动
    sleep 30
    
    # 启动网关
    log_info "启动API网关..."
    docker-compose up -d gateway
    
    # 等待网关启动
    sleep 20
    
    # 启动前端和Nginx
    log_info "启动前端服务..."
    docker-compose up -d frontend nginx
    
    log_success "所有服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    start_services
    log_success "服务重启完成"
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f --tail=100
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查各服务状态
    services=("mysql" "redis" "eureka" "gateway" "user-service" "match-service" "auth-service" "notification-service" "frontend" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps $service | grep -q "Up"; then
            log_success "$service 运行正常"
        else
            log_error "$service 运行异常"
        fi
    done
    
    # 检查API可用性
    log_info "检查API可用性..."
    if curl -f http://localhost/health > /dev/null 2>&1; then
        log_success "API服务可用"
    else
        log_warning "API服务不可用"
    fi
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的卷
    docker volume prune -f
    
    log_success "资源清理完成"
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    BACKUP_DIR="./backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # 备份MySQL数据
    docker-compose exec mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD campus_buddy > $BACKUP_DIR/mysql_backup.sql
    
    # 备份Redis数据
    docker-compose exec redis redis-cli BGSAVE
    docker cp $(docker-compose ps -q redis):/data/dump.rdb $BACKUP_DIR/redis_backup.rdb
    
    log_success "数据备份完成: $BACKUP_DIR"
}

# 显示帮助信息
show_help() {
    echo "校园搭子即时匹配系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境 (默认)"
    echo "  test    测试环境"
    echo "  prod    生产环境"
    echo ""
    echo "操作:"
    echo "  start     启动服务 (默认)"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  build     构建镜像"
    echo "  logs      查看日志"
    echo "  health    健康检查"
    echo "  cleanup   清理资源"
    echo "  backup    备份数据"
    echo "  help      显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 dev start      # 启动开发环境"
    echo "  $0 prod restart   # 重启生产环境"
    echo "  $0 test logs      # 查看测试环境日志"
}

# 主函数
main() {
    case $ACTION in
        start)
            check_dependencies
            set_environment
            start_services
            health_check
            ;;
        stop)
            stop_services
            ;;
        restart)
            check_dependencies
            set_environment
            restart_services
            health_check
            ;;
        build)
            check_dependencies
            set_environment
            build_images
            ;;
        logs)
            show_logs
            ;;
        health)
            health_check
            ;;
        cleanup)
            cleanup
            ;;
        backup)
            backup_data
            ;;
        help)
            show_help
            ;;
        *)
            log_error "不支持的操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main
